# 🐧 Ubuntu系统使用指南

## 🎯 针对您当前遇到的问题

### ✅ 问题已解决！

您遇到的 `AttributeError: type object 'SerialManager' has no attribute 'check_serial_status'` 错误已经修复。

### 🚀 现在可以正常使用的命令

```bash
# 进入项目目录
cd ~/n100host/ctrl

# 检查串口状态
python3 src/power_ctrl_cli.py check

# 强制释放串口锁
python3 src/power_ctrl_cli.py force-release

# 发送LED控制命令
python3 src/power_ctrl_cli.py led normal
python3 src/power_ctrl_cli.py led breath

# 设置呼吸周期
python3 src/power_ctrl_cli.py breath 1
python3 src/power_ctrl_cli.py breath 3
python3 src/power_ctrl_cli.py breath 5

# 发送关机成功消息
python3 src/power_ctrl_cli.py shutdown

# 运行测试序列
python3 src/power_ctrl_cli.py test
```

## 📋 完整的Ubuntu使用流程

### 步骤1：安装依赖
```bash
# 更新包列表
sudo apt update

# 安装Python3和pip（如果还没有）
sudo apt install python3 python3-pip

# 安装pyserial
pip3 install pyserial
```

### 步骤2：配置串口权限
```bash
# 检查串口设备
ls -la /dev/ttyS4

# 如果设备存在，设置权限
sudo chmod 666 /dev/ttyS4

# 将用户添加到dialout组
sudo usermod -a -G dialout $USER

# 重新登录或运行以下命令使组权限生效
newgrp dialout
```

### 步骤3：测试电源控制功能
```bash
# 进入项目目录
cd ~/n100host/ctrl

# 检查串口状态
python3 src/power_ctrl_cli.py check

# 测试LED控制
python3 src/power_ctrl_cli.py led breath
python3 src/power_ctrl_cli.py breath 3

# 运行完整测试
python3 src/power_ctrl_cli.py test
```

### 步骤4：安装关机系统（可选）
```bash
# 运行安装脚本
sudo scripts/install_shutdown_system.sh

# 或者使用修复脚本
sudo scripts/fix_smart_service.sh

# 检查服务状态
systemctl status n100-shutdown-smart.service
```

## 🔧 使用Makefile便捷命令

```bash
# 查看所有可用命令
make help

# 电源控制
make led-normal      # 设置LED正常模式
make led-breath      # 设置LED呼吸模式
make breath-3s       # 设置3秒呼吸周期

# 串口管理
make check           # 检查串口状态
make force-release   # 强制释放串口锁

# 测试
make test            # 运行语法测试
make test-shutdown   # 运行关机系统测试
make example         # 运行使用示例

# 关机系统管理
make install-shutdown  # 安装关机系统
make service-status    # 查看服务状态
make logs             # 查看日志
```

## 🚨 故障排除

### 问题1：串口权限不足
```bash
# 错误信息：Permission denied
sudo chmod 666 /dev/ttyS4
sudo usermod -a -G dialout $USER
```

### 问题2：Python模块导入失败
```bash
# 错误信息：ModuleNotFoundError: No module named 'serial'
pip3 install pyserial
```

### 问题3：串口被占用
```bash
# 检查占用情况
python3 src/power_ctrl_cli.py check

# 停止关机服务
sudo systemctl stop n100-shutdown.service

# 强制释放锁
python3 src/power_ctrl_cli.py force-release
```

### 问题4：服务启动失败
```bash
# 查看服务状态
systemctl status n100-shutdown-smart.service

# 查看详细日志
journalctl -xeu n100-shutdown-smart.service

# 运行修复脚本
sudo scripts/fix_smart_service.sh
```

## 📊 验证系统正常工作

### 1. 基本功能测试
```bash
# 运行语法测试
python3 tests/test_syntax.py
# 应该显示：5/5 个测试通过

# 运行CLI修复测试
python3 tests/test_power_ctrl_cli_fix.py
# 应该显示：4/4 个测试通过

# 运行关机系统测试
python3 tests/test_shutdown_system.py
# 应该显示：4/4 个测试通过
```

### 2. 电源控制测试
```bash
# 测试LED控制
python3 src/power_ctrl_cli.py led normal
python3 src/power_ctrl_cli.py led breath

# 测试呼吸周期
python3 src/power_ctrl_cli.py breath 1
python3 src/power_ctrl_cli.py breath 3
python3 src/power_ctrl_cli.py breath 5

# 测试关机成功消息
python3 src/power_ctrl_cli.py shutdown
```

### 3. 串口管理测试
```bash
# 检查串口状态
python3 src/power_ctrl_cli.py check

# 测试串口锁
python3 tests/test_serial_management.py
```

## 🎯 推荐的日常使用方式

### 方案A：按需使用（推荐）
```bash
# 1. 确保关机服务未占用串口
sudo systemctl stop n100-shutdown.service

# 2. 发送电源控制命令
python3 src/power_ctrl_cli.py led breath
python3 src/power_ctrl_cli.py breath 3

# 3. 需要关机监听时启动服务
sudo systemctl start n100-shutdown.service
```

### 方案B：智能管理
```bash
# 使用自动串口管理
python3 src/power_ctrl_cli.py check        # 自动检查状态
python3 src/power_ctrl_cli.py led breath   # 自动处理冲突
```

## 📝 实际使用示例

### 场景1：设置LED呼吸模式，3秒周期
```bash
cd ~/n100host/ctrl
python3 src/power_ctrl_cli.py check
python3 src/power_ctrl_cli.py led breath
python3 src/power_ctrl_cli.py breath 3
```

### 场景2：发送关机成功消息
```bash
cd ~/n100host/ctrl
python3 src/power_ctrl_cli.py shutdown
```

### 场景3：运行完整测试
```bash
cd ~/n100host/ctrl
python3 src/power_ctrl_cli.py test --delay 2
```

## 🔍 监控和调试

### 查看日志
```bash
# 关机系统日志
tail -f /var/log/n100_shutdown.log

# 系统服务日志
journalctl -u n100-shutdown-smart.service -f
```

### 调试模式
```bash
# 前台运行关机守护进程
python3 src/n100_shutdown_daemon.py --port /dev/ttyS4

# 手动测试关机通知
python3 src/shutdown_notify.py
```

## 🎉 总结

现在您的N100电源控制系统已经完全正常工作：

1. ✅ **电源控制功能** - 可以发送LED和呼吸灯控制命令
2. ✅ **串口管理功能** - 自动检测和处理串口冲突
3. ✅ **关机系统功能** - 智能关机监听和通知
4. ✅ **完整的测试覆盖** - 所有功能都经过验证

您可以放心使用所有功能，如有问题请参考故障排除部分或查看详细文档。
