# 🚀 智能关机服务快速修复指南

## 问题描述
智能关机服务启动失败，错误信息：
```
Job for n100-shutdown-smart.service failed because the control process exited with error code.
```

## 🔧 快速修复步骤

### 步骤1：停止现有服务
```bash
sudo systemctl stop n100-shutdown-smart.service
sudo systemctl stop n100-shutdown.service
```

### 步骤2：运行修复脚本
```bash
# 进入项目目录
cd ~/n100host/ctrl

# 给修复脚本执行权限
chmod +x scripts/fix_smart_service.sh

# 运行修复脚本
sudo scripts/fix_smart_service.sh
```

### 步骤3：手动修复（如果修复脚本不可用）

#### 3.1 创建必要目录
```bash
sudo mkdir -p /opt/n100/ctrl/src
sudo mkdir -p /opt/n100/ctrl/scripts
sudo mkdir -p /var/log
sudo mkdir -p /var/run
```

#### 3.2 复制文件
```bash
# 复制Python源文件
sudo cp src/n100_power_ctrl.py /opt/n100/ctrl/src/
sudo cp src/n100_shutdown_daemon.py /opt/n100/ctrl/src/
sudo cp src/shutdown_notify.py /opt/n100/ctrl/src/
sudo cp src/serial_manager.py /opt/n100/ctrl/src/

# 复制脚本文件
sudo cp scripts/smart_shutdown_start.sh /opt/n100/ctrl/scripts/
sudo cp scripts/smart_shutdown_stop.sh /opt/n100/ctrl/scripts/
sudo cp scripts/n100-shutdown-hook.sh /opt/n100/ctrl/scripts/

# 设置执行权限
sudo chmod +x /opt/n100/ctrl/src/*.py
sudo chmod +x /opt/n100/ctrl/scripts/*.sh
```

#### 3.3 安装服务文件
```bash
# 复制服务文件
sudo cp services/n100-shutdown-smart.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload
```

#### 3.4 配置权限
```bash
# 配置串口权限（如果设备存在）
sudo chmod 666 /dev/ttyS4 2>/dev/null || true

# 创建日志文件
sudo touch /var/log/n100_shutdown.log
sudo chmod 644 /var/log/n100_shutdown.log
```

### 步骤4：启动服务
```bash
# 启用服务
sudo systemctl enable n100-shutdown-smart.service

# 启动服务
sudo systemctl start n100-shutdown-smart.service
```

### 步骤5：验证服务状态
```bash
# 检查服务状态
systemctl status n100-shutdown-smart.service

# 查看日志
tail -f /var/log/n100_shutdown.log
```

## 🔍 故障排除

### 如果服务仍然失败

#### 检查详细错误信息
```bash
journalctl -xeu n100-shutdown-smart.service
```

#### 检查文件是否存在
```bash
ls -la /opt/n100/ctrl/src/
ls -la /opt/n100/ctrl/scripts/
ls -la /etc/systemd/system/n100-shutdown-smart.service
```

#### 手动测试启动脚本
```bash
sudo /opt/n100/ctrl/scripts/smart_shutdown_start.sh
```

#### 检查Python依赖
```bash
python3 -c "import serial; print('pyserial OK')" 2>/dev/null || echo "需要安装: pip3 install pyserial"
```

### 常见问题解决

#### 问题1：权限不足
```bash
sudo chown -R root:root /opt/n100/ctrl/
sudo chmod +x /opt/n100/ctrl/scripts/*.sh
```

#### 问题2：Python路径问题
```bash
# 检查Python3是否可用
which python3

# 如果不存在，安装Python3
sudo apt update
sudo apt install python3 python3-pip
```

#### 问题3：串口设备不存在
```bash
# 检查串口设备
ls -la /dev/ttyS*

# 如果ttyS4不存在，这是正常的（在虚拟环境中）
# 服务会记录警告但继续运行
```

## 🎯 验证修复结果

### 成功的标志
1. 服务状态显示 `active (exited)`
2. 日志文件中有启动记录
3. 没有错误信息

### 测试命令
```bash
# 检查服务状态
systemctl is-active n100-shutdown-smart.service

# 查看最近日志
tail -5 /var/log/n100_shutdown.log

# 测试串口管理
python3 src/power_ctrl_cli.py check
```

## 📞 如果仍有问题

请提供以下信息：

1. **服务状态**:
   ```bash
   systemctl status n100-shutdown-smart.service
   ```

2. **详细日志**:
   ```bash
   journalctl -xeu n100-shutdown-smart.service --no-pager
   ```

3. **文件检查**:
   ```bash
   ls -la /opt/n100/ctrl/scripts/smart_shutdown_start.sh
   cat /var/log/n100_shutdown.log
   ```

4. **系统信息**:
   ```bash
   uname -a
   python3 --version
   ```

## 🔄 替代方案

如果智能服务仍有问题，可以使用按需启动方案：

```bash
# 禁用智能服务
sudo systemctl disable n100-shutdown-smart.service
sudo systemctl stop n100-shutdown-smart.service

# 使用按需启动
sudo systemctl disable n100-shutdown.service  # 禁用自动启动

# 需要关机监听时手动启动
sudo systemctl start n100-shutdown.service

# 不需要时停止
sudo systemctl stop n100-shutdown.service
```

这样可以完全避免串口冲突问题。
