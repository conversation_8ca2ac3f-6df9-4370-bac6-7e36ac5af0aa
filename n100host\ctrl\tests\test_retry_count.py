#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重试次数配置
验证默认重试次数已从3次改为10次
"""

import os
import sys
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))


def test_default_retry_count():
    """测试默认重试次数"""
    print("=== 测试默认重试次数 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from n100_power_ctrl import N100PowerController
            
            # 测试默认构造函数
            controller = N100PowerController()
            
            if controller.max_retries == 10:
                print("✓ 默认重试次数正确设置为10次")
                return True
            else:
                print(f"✗ 默认重试次数错误: 期望10，实际{controller.max_retries}")
                return False
                
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
            return False


def test_custom_retry_count():
    """测试自定义重试次数"""
    print("\n=== 测试自定义重试次数 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from n100_power_ctrl import N100PowerController
            
            # 测试自定义重试次数
            custom_retries = 5
            controller = N100PowerController(max_retries=custom_retries)
            
            if controller.max_retries == custom_retries:
                print(f"✓ 自定义重试次数正确设置为{custom_retries}次")
                return True
            else:
                print(f"✗ 自定义重试次数错误: 期望{custom_retries}，实际{controller.max_retries}")
                return False
                
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
            return False


def test_cli_default_retry_count():
    """测试CLI工具的默认重试次数"""
    print("\n=== 测试CLI工具默认重试次数 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            import power_ctrl_cli
            import argparse
            
            # 创建解析器（模拟CLI参数解析）
            parser = argparse.ArgumentParser()
            parser.add_argument('--retries', type=int, default=10)
            
            # 解析空参数（使用默认值）
            args = parser.parse_args([])
            
            if args.retries == 10:
                print("✓ CLI工具默认重试次数正确设置为10次")
                return True
            else:
                print(f"✗ CLI工具默认重试次数错误: 期望10，实际{args.retries}")
                return False
                
        except ImportError as e:
            print(f"✗ 导入CLI模块失败: {e}")
            return False


def test_example_retry_count():
    """测试示例程序的重试次数"""
    print("\n=== 测试示例程序重试次数 ===")
    
    # 读取示例文件内容
    example_file = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
        'examples', 'example.py'
    )
    
    try:
        with open(example_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否包含max_retries=10
        if 'max_retries=10' in content:
            print("✓ 示例程序重试次数正确设置为10次")
            return True
        elif 'max_retries=3' in content:
            print("✗ 示例程序仍使用旧的重试次数3次")
            return False
        else:
            print("? 示例程序中未找到明确的重试次数设置")
            return False
            
    except FileNotFoundError:
        print(f"✗ 示例文件不存在: {example_file}")
        return False
    except Exception as e:
        print(f"✗ 读取示例文件失败: {e}")
        return False


def test_documentation_update():
    """测试文档更新"""
    print("\n=== 测试文档更新 ===")
    
    # 检查主要文档文件
    doc_files = [
        ('README.md', '最大重试次数: `10次`'),
        ('docs/USAGE.md', 'max_retries=10'),
        ('docs/README.md', 'max_retries=10')
    ]
    
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    success_count = 0
    
    for doc_file, expected_content in doc_files:
        file_path = os.path.join(base_dir, doc_file)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if expected_content in content:
                print(f"✓ {doc_file} 已正确更新")
                success_count += 1
            else:
                print(f"✗ {doc_file} 未正确更新")
                
        except FileNotFoundError:
            print(f"? {doc_file} 文件不存在")
        except Exception as e:
            print(f"✗ 读取 {doc_file} 失败: {e}")
    
    return success_count == len(doc_files)


def test_retry_behavior_simulation():
    """模拟测试重试行为"""
    print("\n=== 模拟测试重试行为 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial_instance = MagicMock()
    mock_serial.Serial.return_value = mock_serial_instance
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    # 模拟串口连接失败的情况
    mock_serial_instance.is_open = False
    mock_serial_instance.in_waiting = 0
    mock_serial_instance.read.return_value = b''
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from n100_power_ctrl import N100PowerController
            
            controller = N100PowerController()
            
            # 模拟连接
            mock_serial_instance.is_open = True
            controller.serial = mock_serial_instance
            controller.is_connected = True
            
            # 计算预期的重试次数
            expected_retries = controller.max_retries
            
            # 模拟发送命令（会失败并重试）
            with patch('builtins.print') as mock_print:
                result = controller._send_frame_with_retry(b'\xAA\x01\x03\xFD\x55', '测试命令')
                
                # 检查打印的重试信息
                print_calls = [str(call) for call in mock_print.call_args_list]
                retry_messages = [call for call in print_calls if '尝试' in call and f'/{expected_retries}' in call]
                
                if len(retry_messages) >= expected_retries:
                    print(f"✓ 重试行为正确，执行了{expected_retries}次尝试")
                    return True
                else:
                    print(f"✗ 重试行为异常，预期{expected_retries}次，实际{len(retry_messages)}次")
                    return False
                    
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
            return False
        except Exception as e:
            print(f"✗ 模拟测试失败: {e}")
            return False


def main():
    """主测试函数"""
    print("重试次数配置测试")
    print("=" * 50)
    
    tests = [
        ("默认重试次数", test_default_retry_count),
        ("自定义重试次数", test_custom_retry_count),
        ("CLI工具默认重试次数", test_cli_default_retry_count),
        ("示例程序重试次数", test_example_retry_count),
        ("文档更新", test_documentation_update),
        ("重试行为模拟", test_retry_behavior_simulation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重试次数已成功从3次改为10次。")
        print("\n配置更新总结:")
        print("- N100PowerController默认重试次数: 3 → 10")
        print("- power_ctrl_cli.py默认重试次数: 3 → 10")
        print("- example.py重试次数: 3 → 10")
        print("- 相关文档已更新")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
