[Unit]
Description=N100 Smart Shutdown Daemon
Documentation=N100 power board shutdown request monitor (smart mode)
After=network.target
Wants=network.target

[Service]
Type=oneshot
RemainAfterExit=yes
User=root
Group=root

# 启动脚本：检查是否需要启动关机监听
ExecStart=/opt/n100/ctrl/smart_shutdown_start.sh

# 停止脚本：清理资源
ExecStop=/opt/n100/ctrl/smart_shutdown_stop.sh

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /tmp /var/lock

[Install]
WantedBy=multi-user.target
