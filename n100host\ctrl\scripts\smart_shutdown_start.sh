#!/bin/bash
# N100智能关机启动脚本
# 检查串口状态，如果空闲则启动关机监听

SCRIPT_DIR="/opt/n100/ctrl"
LOG_FILE="/var/log/n100_shutdown.log"
PID_FILE="/var/run/n100_shutdown.pid"

# 日志函数
log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查串口是否被占用
check_serial_available() {
    python3 "$SCRIPT_DIR/serial_manager.py" --check > /dev/null 2>&1
    return $?
}

# 启动关机守护进程
start_shutdown_daemon() {
    log_msg "启动关机守护进程..."
    
    # 后台启动守护进程
    nohup python3 "$SCRIPT_DIR/n100_shutdown_daemon.py" --port /dev/ttyS4 >> "$LOG_FILE" 2>&1 &
    
    # 记录PID
    echo $! > "$PID_FILE"
    
    log_msg "关机守护进程已启动，PID: $(cat $PID_FILE)"
}

# 主逻辑
main() {
    log_msg "智能关机服务启动检查..."
    
    # 检查串口是否可用
    if check_serial_available; then
        log_msg "串口可用，启动关机监听"
        start_shutdown_daemon
    else
        log_msg "串口被占用，跳过关机监听启动"
        log_msg "提示: 可以手动启动关机监听: systemctl start n100-shutdown.service"
    fi
    
    log_msg "智能关机服务启动检查完成"
}

# 执行主函数
main
