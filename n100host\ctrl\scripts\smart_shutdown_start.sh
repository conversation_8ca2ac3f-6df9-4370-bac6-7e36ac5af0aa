#!/bin/bash
# N100智能关机启动脚本
# 检查串口状态，如果空闲则启动关机监听

SCRIPT_DIR="/opt/n100/ctrl"
LOG_FILE="/var/log/n100_shutdown.log"
PID_FILE="/var/run/n100_shutdown.pid"

# 确保日志文件存在
touch "$LOG_FILE" 2>/dev/null || true

# 日志函数
log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查串口是否被占用（简化版本）
check_serial_available() {
    # 简单检查：如果PID文件存在且进程在运行，则认为串口被占用
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$PID" ] && kill -0 "$PID" 2>/dev/null; then
            return 1  # 串口被占用
        else
            # PID文件存在但进程不在，清理PID文件
            rm -f "$PID_FILE" 2>/dev/null || true
        fi
    fi

    # 检查是否有其他进程在使用串口（简化检查）
    if pgrep -f "ttyS4" > /dev/null 2>&1; then
        return 1  # 串口被占用
    fi

    return 0  # 串口可用
}

# 启动关机守护进程
start_shutdown_daemon() {
    log_msg "启动关机守护进程..."

    # 检查Python脚本是否存在
    if [ ! -f "$SCRIPT_DIR/src/n100_shutdown_daemon.py" ]; then
        log_msg "错误: 找不到关机守护进程脚本"
        return 1
    fi

    # 检查串口设备是否存在
    if [ ! -e "/dev/ttyS4" ]; then
        log_msg "警告: 串口设备 /dev/ttyS4 不存在"
    fi

    # 后台启动守护进程
    cd "$SCRIPT_DIR" || return 1
    nohup python3 src/n100_shutdown_daemon.py --port /dev/ttyS4 >> "$LOG_FILE" 2>&1 &

    # 记录PID
    echo $! > "$PID_FILE"

    log_msg "关机守护进程已启动，PID: $(cat $PID_FILE 2>/dev/null || echo 'unknown')"
}

# 主逻辑
main() {
    log_msg "智能关机服务启动检查..."

    # 检查必要的目录和文件
    if [ ! -d "$SCRIPT_DIR" ]; then
        log_msg "错误: 程序目录不存在: $SCRIPT_DIR"
        exit 1
    fi

    # 检查串口是否可用
    if check_serial_available; then
        log_msg "串口可用，启动关机监听"
        if start_shutdown_daemon; then
            log_msg "关机守护进程启动成功"
        else
            log_msg "关机守护进程启动失败"
            exit 1
        fi
    else
        log_msg "串口被占用，跳过关机监听启动"
        log_msg "提示: 可以手动启动关机监听: systemctl start n100-shutdown.service"
    fi

    log_msg "智能关机服务启动检查完成"
}

# 执行主函数
main
