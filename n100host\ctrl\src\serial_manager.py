#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100串口管理器
管理串口资源，避免多个程序同时占用串口
"""

import os
import sys
import time
import signal
import threading
from typing import Optional, Callable
from pathlib import Path

# 跨平台文件锁支持
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False
    # Windows平台使用msvcrt
    try:
        import msvcrt
        HAS_MSVCRT = True
    except ImportError:
        HAS_MSVCRT = False

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, PowerCommand
except ImportError as e:
    print(f"错误: 无法导入n100_power_ctrl模块: {e}")
    sys.exit(1)


class SerialManager:
    """串口管理器类"""
    
    LOCK_FILE = "/var/lock/n100_serial.lock"
    
    def __init__(self, port: str = '/dev/ttyS4'):
        """
        初始化串口管理器
        
        参数:
            port (str): 串口设备路径
        """
        self.port = port
        self.lock_fd = None
        self.controller = None
        self.is_locked = False
        
    def acquire_lock(self, timeout: float = 5.0) -> bool:
        """
        获取串口锁

        参数:
            timeout (float): 超时时间(秒)

        返回:
            bool: 是否成功获取锁
        """
        try:
            # 创建锁文件目录
            os.makedirs(os.path.dirname(self.LOCK_FILE), exist_ok=True)

            # 简化的锁机制：检查锁文件是否存在
            start_time = time.time()
            while time.time() - start_time < timeout:
                if not os.path.exists(self.LOCK_FILE):
                    try:
                        # 创建锁文件
                        with open(self.LOCK_FILE, 'w') as f:
                            f.write(f"{os.getpid()}\n{time.time()}\n")

                        self.is_locked = True
                        print(f"[锁] 成功获取串口锁: {self.LOCK_FILE}")
                        return True
                    except Exception:
                        pass

                time.sleep(0.1)

            print(f"[锁] 获取串口锁超时: {timeout}秒")
            return False

        except Exception as e:
            print(f"[锁] 获取串口锁失败: {e}")
            return False
    
    def release_lock(self):
        """释放串口锁"""
        if self.is_locked:
            try:
                # 删除锁文件
                if os.path.exists(self.LOCK_FILE):
                    os.remove(self.LOCK_FILE)

                self.is_locked = False
                print(f"[锁] 已释放串口锁")

            except Exception as e:
                print(f"[锁] 释放串口锁失败: {e}")
    
    def connect_controller(self) -> bool:
        """
        连接电源控制器
        
        返回:
            bool: 是否连接成功
        """
        if not self.is_locked:
            print("[错误] 请先获取串口锁")
            return False
        
        try:
            self.controller = N100PowerController(port=self.port)
            if self.controller.connect():
                print(f"[连接] 成功连接到串口 {self.port}")
                return True
            else:
                print(f"[连接] 连接串口失败 {self.port}")
                return False
        except Exception as e:
            print(f"[连接] 连接异常: {e}")
            return False
    
    def disconnect_controller(self):
        """断开电源控制器连接"""
        if self.controller:
            self.controller.disconnect()
            self.controller = None
            print("[连接] 已断开串口连接")
    
    def send_command(self, command_func: Callable) -> bool:
        """
        发送命令
        
        参数:
            command_func: 命令函数
            
        返回:
            bool: 是否发送成功
        """
        if not self.controller:
            print("[错误] 控制器未连接")
            return False
        
        try:
            return command_func()
        except Exception as e:
            print(f"[错误] 发送命令失败: {e}")
            return False
    
    def monitor_shutdown_requests(self, timeout: float = None) -> bool:
        """
        监听关机请求
        
        参数:
            timeout (float): 监听超时时间(秒)
            
        返回:
            bool: 是否收到关机请求
        """
        if not self.controller:
            print("[错误] 控制器未连接")
            return False
        
        try:
            return self.controller.monitor_shutdown_requests(timeout)
        except Exception as e:
            print(f"[错误] 监听关机请求失败: {e}")
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        if self.acquire_lock():
            if self.connect_controller():
                return self
        raise RuntimeError("无法获取串口资源")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect_controller()
        self.release_lock()


def check_serial_status():
    """检查串口状态"""
    lock_file = SerialManager.LOCK_FILE
    
    if os.path.exists(lock_file):
        try:
            with open(lock_file, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    pid = int(lines[0].strip())
                    timestamp = float(lines[1].strip())
                    
                    # 检查进程是否还在运行
                    try:
                        os.kill(pid, 0)  # 发送信号0检查进程是否存在
                        print(f"串口被进程 {pid} 占用 (锁定时间: {time.ctime(timestamp)})")
                        return False
                    except OSError:
                        # 进程不存在，删除锁文件
                        os.remove(lock_file)
                        print("发现僵尸锁文件，已清理")
                        return True
        except Exception as e:
            print(f"检查锁文件失败: {e}")
            return False
    
    print("串口可用")
    return True


def force_release_lock():
    """强制释放串口锁"""
    lock_file = SerialManager.LOCK_FILE
    
    if os.path.exists(lock_file):
        try:
            os.remove(lock_file)
            print("已强制释放串口锁")
            return True
        except Exception as e:
            print(f"强制释放锁失败: {e}")
            return False
    else:
        print("没有发现锁文件")
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='N100串口管理器')
    parser.add_argument('--port', type=str, default='/dev/ttyS4',
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--check', action='store_true',
                       help='检查串口状态')
    parser.add_argument('--force-release', action='store_true',
                       help='强制释放串口锁')
    parser.add_argument('--test', action='store_true',
                       help='测试串口管理器')
    
    args = parser.parse_args()
    
    if args.check:
        check_serial_status()
        return
    
    if args.force_release:
        force_release_lock()
        return
    
    if args.test:
        # 测试串口管理器
        print("测试串口管理器...")
        
        try:
            with SerialManager(args.port) as manager:
                print("成功获取串口资源")
                
                # 测试发送命令
                success = manager.send_command(lambda: manager.controller.set_led_normal())
                print(f"发送LED命令: {'成功' if success else '失败'}")
                
                time.sleep(1)
                
        except Exception as e:
            print(f"测试失败: {e}")
        
        return
    
    parser.print_help()


if __name__ == "__main__":
    main()
