#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试power_ctrl_cli.py修复后的功能
验证串口管理功能是否正常工作
"""

import os
import sys
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))


def test_cli_import():
    """测试CLI模块导入"""
    print("=== 测试CLI模块导入 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            # 导入CLI模块
            import power_ctrl_cli
            print("✓ power_ctrl_cli模块导入成功")
            
            # 检查是否有必要的函数
            if hasattr(power_ctrl_cli, 'main'):
                print("✓ main函数存在")
            else:
                print("✗ main函数不存在")
                return False
            
            return True
            
        except ImportError as e:
            print(f"✗ CLI模块导入失败: {e}")
            return False


def test_serial_manager_functions():
    """测试串口管理器函数"""
    print("\n=== 测试串口管理器函数 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from serial_manager import SerialManager, check_serial_status, force_release_lock
            
            print("✓ 串口管理器模块导入成功")
            
            # 测试check_serial_status函数
            try:
                result = check_serial_status()
                print(f"✓ check_serial_status函数调用成功，返回: {result}")
            except Exception as e:
                print(f"✗ check_serial_status函数调用失败: {e}")
                return False
            
            # 测试force_release_lock函数
            try:
                result = force_release_lock()
                print(f"✓ force_release_lock函数调用成功，返回: {result}")
            except Exception as e:
                print(f"✗ force_release_lock函数调用失败: {e}")
                return False
            
            return True
            
        except ImportError as e:
            print(f"✗ 串口管理器模块导入失败: {e}")
            return False


def test_cli_check_command():
    """测试CLI的check命令"""
    print("\n=== 测试CLI的check命令 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            import power_ctrl_cli
            
            # 模拟命令行参数
            test_args = ['power_ctrl_cli.py', 'check']
            
            with patch('sys.argv', test_args):
                try:
                    # 捕获退出码
                    with patch('sys.exit') as mock_exit:
                        power_ctrl_cli.main()
                        
                        # 检查是否正常退出
                        if mock_exit.called:
                            exit_code = mock_exit.call_args[0][0] if mock_exit.call_args[0] else 0
                            if exit_code == 0:
                                print("✓ check命令执行成功")
                                return True
                            else:
                                print(f"✗ check命令执行失败，退出码: {exit_code}")
                                return False
                        else:
                            print("✓ check命令执行成功（未调用exit）")
                            return True
                            
                except Exception as e:
                    print(f"✗ check命令执行异常: {e}")
                    return False
                    
        except ImportError as e:
            print(f"✗ CLI模块导入失败: {e}")
            return False


def test_cli_force_release_command():
    """测试CLI的force-release命令"""
    print("\n=== 测试CLI的force-release命令 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            import power_ctrl_cli
            
            # 模拟命令行参数
            test_args = ['power_ctrl_cli.py', 'force-release']
            
            with patch('sys.argv', test_args):
                try:
                    # 捕获退出码
                    with patch('sys.exit') as mock_exit:
                        power_ctrl_cli.main()
                        
                        # 检查是否正常退出
                        if mock_exit.called:
                            exit_code = mock_exit.call_args[0][0] if mock_exit.call_args[0] else 0
                            if exit_code == 0:
                                print("✓ force-release命令执行成功")
                                return True
                            else:
                                print(f"✗ force-release命令执行失败，退出码: {exit_code}")
                                return False
                        else:
                            print("✓ force-release命令执行成功（未调用exit）")
                            return True
                            
                except Exception as e:
                    print(f"✗ force-release命令执行异常: {e}")
                    return False
                    
        except ImportError as e:
            print(f"✗ CLI模块导入失败: {e}")
            return False


def main():
    """主测试函数"""
    print("power_ctrl_cli.py修复验证测试")
    print("=" * 50)
    
    tests = [
        ("CLI模块导入", test_cli_import),
        ("串口管理器函数", test_serial_manager_functions),
        ("CLI check命令", test_cli_check_command),
        ("CLI force-release命令", test_cli_force_release_command),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！power_ctrl_cli.py修复成功。")
        print("\n现在可以正常使用以下命令:")
        print("  python3 src/power_ctrl_cli.py check")
        print("  python3 src/power_ctrl_cli.py force-release")
        print("  python3 src/power_ctrl_cli.py led normal")
        print("  python3 src/power_ctrl_cli.py led breath")
        return 0
    else:
        print("❌ 部分测试失败，请检查代码。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
