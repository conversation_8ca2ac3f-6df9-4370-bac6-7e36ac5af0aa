#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机守护进程
监听电源板发送的关机请求，执行关机流程，并在文件系统关闭后发送关机成功消息

功能：
1. 监听串口ttyS4，接收电源板的关机请求 (CMD_SHUTDOWN_REQ = 0x13)
2. 收到关机请求后立即回复通用应答帧 (CMD_ACK = 0x80)
3. 执行系统关机命令
4. 在文件系统关闭前的最后阶段发送关机成功消息 (CMD_SHUTDOWN = 0x03)
"""

import os
import sys
import time
import signal
import threading
import subprocess
import logging
from pathlib import Path
from typing import Optional

# 添加当前目录到Python路径，以便导入n100_power_ctrl模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, PowerCommand
except ImportError as e:
    print(f"错误: 无法导入n100_power_ctrl模块: {e}")
    print("请确保n100_power_ctrl.py文件在同一目录下")
    sys.exit(1)


class ShutdownCommand:
    """关机请求命令定义"""
    SHUTDOWN_REQ = 0x13  # 电源板发送的关机请求命令


class N100ShutdownDaemon:
    """N100关机守护进程类"""
    
    def __init__(self, port: str = '/dev/ttyS4', log_file: str = '/var/log/n100_shutdown.log'):
        """
        初始化关机守护进程
        
        参数:
            port (str): 串口设备路径
            log_file (str): 日志文件路径
        """
        self.port = port
        self.log_file = log_file
        self.controller = None
        self.running = False
        self.shutdown_requested = False
        
        # 设置日志
        self._setup_logging()
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        self.logger.info("N100关机守护进程初始化完成")
    
    def _setup_logging(self):
        """设置日志记录"""
        # 确保日志目录存在
        log_dir = os.path.dirname(self.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger('N100ShutdownDaemon')
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备退出...")
        self.stop()
    
    def start(self):
        """启动守护进程"""
        self.logger.info("启动N100关机守护进程...")
        
        # 创建电源控制器
        self.controller = N100PowerController(port=self.port, timeout=0.1)
        
        # 设置回调函数
        self.controller.set_error_callback(self._on_error)
        
        # 连接串口
        if not self.controller.connect():
            self.logger.error(f"无法连接到串口 {self.port}")
            return False
        
        self.running = True
        self.logger.info("关机守护进程已启动，开始监听关机请求...")
        
        try:
            self._monitor_shutdown_requests()
        except Exception as e:
            self.logger.error(f"监听过程中发生异常: {e}")
            return False
        finally:
            self.stop()
        
        return True
    
    def stop(self):
        """停止守护进程"""
        if self.running:
            self.logger.info("停止关机守护进程...")
            self.running = False
            
            if self.controller:
                self.controller.disconnect()
    
    def _on_error(self, error_msg: str):
        """错误回调函数"""
        self.logger.error(f"串口通信错误: {error_msg}")
    
    def _monitor_shutdown_requests(self):
        """监听关机请求"""
        self.logger.info("开始监听电源板关机请求...")
        
        while self.running:
            try:
                # 检查是否有数据可读
                if self.controller.serial.in_waiting > 0:
                    # 读取数据
                    data = self.controller.serial.read(self.controller.serial.in_waiting)
                    self.logger.debug(f"接收到数据: {data.hex(' ').upper()}")
                    
                    # 处理接收到的数据
                    self._process_received_data(data)
                
                # 短暂休眠避免CPU占用过高
                time.sleep(0.01)
                
            except Exception as e:
                self.logger.error(f"监听过程中发生错误: {e}")
                time.sleep(1)  # 发生错误时等待1秒再继续
    
    def _process_received_data(self, data: bytes):
        """处理接收到的数据"""
        # 查找关机请求帧
        # 期望的关机请求帧格式: AA 01 13 ED 55 (根据协议计算)
        # 帧头(AA) + 长度(01) + 命令(13) + 校验和 + 帧尾(55)
        
        for i in range(len(data)):
            if data[i] == 0xAA and i + 4 < len(data):  # 找到帧头且有足够的数据
                # 检查是否为关机请求帧
                if (data[i + 1] == 0x01 and  # 长度为1（只有命令）
                    data[i + 2] == ShutdownCommand.SHUTDOWN_REQ and  # 关机请求命令
                    data[i + 4] == 0x55):  # 帧尾
                    
                    self.logger.info("收到电源板关机请求!")
                    
                    # 立即发送ACK应答
                    self._send_ack_response()
                    
                    # 执行关机流程
                    self._execute_shutdown()
                    
                    return
    
    def _send_ack_response(self):
        """发送ACK应答"""
        try:
            # 创建通用应答帧: AA 01 80 80 55
            ack_frame = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            
            self.controller.serial.write(ack_frame)
            self.controller.serial.flush()
            
            self.logger.info("已发送ACK应答给电源板")
            
        except Exception as e:
            self.logger.error(f"发送ACK应答失败: {e}")
    
    def _execute_shutdown(self):
        """执行关机流程"""
        if self.shutdown_requested:
            self.logger.warning("关机流程已在进行中，忽略重复请求")
            return
        
        self.shutdown_requested = True
        self.logger.info("开始执行关机流程...")
        
        try:
            # 创建关机脚本，在系统关机的最后阶段发送关机成功消息
            self._create_shutdown_script()
            
            # 等待一小段时间确保ACK发送完成
            time.sleep(0.5)
            
            # 执行系统关机命令
            self.logger.info("执行系统关机命令...")
            subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=False)
            
        except Exception as e:
            self.logger.error(f"执行关机流程失败: {e}")
    
    def _create_shutdown_script(self):
        """创建关机脚本，在系统关机时发送关机成功消息"""
        script_content = f'''#!/bin/bash
# N100关机成功通知脚本
# 在系统关机的最后阶段发送关机成功消息给电源板

# 日志函数
log_msg() {{
    echo "$(date): $1" >> /var/log/n100_shutdown.log
}}

log_msg "关机脚本开始执行..."

# 等待文件系统同步
sync
log_msg "文件系统同步完成"

# 发送关机成功消息
python3 {os.path.abspath(__file__)} --send-shutdown-success --port {self.port}

log_msg "关机成功消息已发送"
'''
        
        # 写入关机脚本
        script_path = '/etc/init.d/n100_shutdown_notify'
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            # 设置执行权限
            os.chmod(script_path, 0o755)
            
            # 添加到关机序列
            subprocess.run(['sudo', 'update-rc.d', 'n100_shutdown_notify', 'start', '01', '0', '6', '.'], 
                          check=False)
            
            self.logger.info(f"关机脚本已创建: {script_path}")
            
        except Exception as e:
            self.logger.error(f"创建关机脚本失败: {e}")
    
    @staticmethod
    def send_shutdown_success(port: str = '/dev/ttyS4'):
        """发送关机成功消息（静态方法，供关机脚本调用）"""
        try:
            # 创建控制器
            controller = N100PowerController(port=port, timeout=0.5, max_retries=1)
            
            if controller.connect():
                # 发送关机成功消息
                success = controller.send_shutdown_success()
                controller.disconnect()
                
                if success:
                    print("关机成功消息已发送")
                    return True
                else:
                    print("发送关机成功消息失败")
                    return False
            else:
                print(f"无法连接到串口 {port}")
                return False
                
        except Exception as e:
            print(f"发送关机成功消息时发生异常: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='N100关机守护进程')
    parser.add_argument('--port', type=str, default='/dev/ttyS4', 
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--log-file', type=str, default='/var/log/n100_shutdown.log',
                       help='日志文件路径 (默认: /var/log/n100_shutdown.log)')
    parser.add_argument('--daemon', action='store_true',
                       help='以守护进程模式运行')
    parser.add_argument('--send-shutdown-success', action='store_true',
                       help='仅发送关机成功消息（供关机脚本调用）')
    
    args = parser.parse_args()
    
    # 如果只是发送关机成功消息
    if args.send_shutdown_success:
        success = N100ShutdownDaemon.send_shutdown_success(args.port)
        sys.exit(0 if success else 1)
    
    # 创建守护进程
    daemon = N100ShutdownDaemon(port=args.port, log_file=args.log_file)
    
    if args.daemon:
        # 以守护进程模式运行
        try:
            import daemon
            with daemon.DaemonContext():
                daemon.start()
        except ImportError:
            print("错误: 需要安装python-daemon包才能以守护进程模式运行")
            print("请运行: pip install python-daemon")
            sys.exit(1)
    else:
        # 前台运行
        try:
            daemon.start()
        except KeyboardInterrupt:
            print("\n用户中断，退出程序")
            sys.exit(0)


if __name__ == "__main__":
    main()
