# 🔧 N100关机系统故障排除指南

## 🚨 当前问题：智能关机服务启动失败

### 错误现象
```bash
user@user-Default-string:~/n100host/ctrl$ sudo systemctl start n100-shutdown-smart.service
Job for n100-shutdown-smart.service failed because the control process exited with error code.
```

### 🎯 立即解决方案

#### 方案1：使用诊断脚本（推荐）
```bash
# 进入项目目录
cd ~/n100host/ctrl

# 运行诊断脚本
chmod +x scripts/diagnose.sh
./scripts/diagnose.sh
```

#### 方案2：手动快速修复
```bash
# 1. 停止所有相关服务
sudo systemctl stop n100-shutdown-smart.service
sudo systemctl stop n100-shutdown.service

# 2. 检查并创建必要目录
sudo mkdir -p /opt/n100/ctrl/{src,scripts}
sudo mkdir -p /var/{log,run}

# 3. 复制文件到正确位置
sudo cp src/*.py /opt/n100/ctrl/src/
sudo cp scripts/*.sh /opt/n100/ctrl/scripts/
sudo cp services/n100-shutdown-smart.service /etc/systemd/system/

# 4. 设置权限
sudo chmod +x /opt/n100/ctrl/src/*.py
sudo chmod +x /opt/n100/ctrl/scripts/*.sh

# 5. 重新加载并启动服务
sudo systemctl daemon-reload
sudo systemctl start n100-shutdown-smart.service
```

#### 方案3：使用修复脚本
```bash
# 运行自动修复脚本
sudo scripts/fix_smart_service.sh
```

### 🔍 问题诊断步骤

#### 1. 检查服务状态
```bash
systemctl status n100-shutdown-smart.service
journalctl -xeu n100-shutdown-smart.service
```

#### 2. 检查文件是否存在
```bash
ls -la /opt/n100/ctrl/scripts/smart_shutdown_start.sh
ls -la /etc/systemd/system/n100-shutdown-smart.service
```

#### 3. 检查脚本权限
```bash
ls -la /opt/n100/ctrl/scripts/
```

#### 4. 手动测试脚本
```bash
sudo /opt/n100/ctrl/scripts/smart_shutdown_start.sh
```

### 🛠️ 常见问题及解决方法

#### 问题1：文件不存在
**症状**: `No such file or directory`
**解决**: 
```bash
# 确保所有文件都复制到了正确位置
sudo cp -r src/ /opt/n100/ctrl/
sudo cp -r scripts/ /opt/n100/ctrl/
```

#### 问题2：权限不足
**症状**: `Permission denied`
**解决**:
```bash
sudo chmod +x /opt/n100/ctrl/scripts/*.sh
sudo chown -R root:root /opt/n100/ctrl/
```

#### 问题3：Python依赖缺失
**症状**: `ModuleNotFoundError: No module named 'serial'`
**解决**:
```bash
pip3 install pyserial
```

#### 问题4：串口设备不存在
**症状**: 日志中显示 `串口设备 /dev/ttyS4 不存在`
**解决**: 这在虚拟环境中是正常的，服务会继续运行

### 🔄 替代解决方案

如果智能服务仍有问题，可以使用以下替代方案：

#### 替代方案1：按需启动（最简单）
```bash
# 禁用所有自动启动的关机服务
sudo systemctl disable n100-shutdown-smart.service
sudo systemctl disable n100-shutdown.service

# 需要关机监听时手动启动
sudo systemctl start n100-shutdown.service

# 需要发送电源控制命令时先停止服务
sudo systemctl stop n100-shutdown.service
python3 src/power_ctrl_cli.py led breath
```

#### 替代方案2：使用串口管理器
```bash
# 使用新的CLI工具（自动处理冲突）
python3 src/power_ctrl_cli.py check          # 检查串口状态
python3 src/power_ctrl_cli.py force-release  # 强制释放串口
python3 src/power_ctrl_cli.py led breath     # 发送命令
```

### 📋 验证修复结果

#### 成功标志
1. 服务状态显示正常：
   ```bash
   systemctl status n100-shutdown-smart.service
   # 应该显示 "active (exited)" 或 "active (running)"
   ```

2. 日志文件有记录：
   ```bash
   tail /var/log/n100_shutdown.log
   # 应该有启动记录
   ```

3. 电源控制命令正常工作：
   ```bash
   python3 src/power_ctrl_cli.py check
   # 应该显示串口状态
   ```

### 🆘 如果仍然无法解决

请收集以下信息并寻求帮助：

#### 1. 系统信息
```bash
uname -a
python3 --version
systemctl --version
```

#### 2. 服务详细状态
```bash
systemctl status n100-shutdown-smart.service -l
journalctl -xeu n100-shutdown-smart.service --no-pager
```

#### 3. 文件结构检查
```bash
find /opt/n100/ctrl -type f -ls 2>/dev/null
ls -la /etc/systemd/system/n100-shutdown*.service
```

#### 4. 日志内容
```bash
cat /var/log/n100_shutdown.log
```

### 📞 联系支持

如果问题持续存在，请：

1. 运行诊断脚本：`./scripts/diagnose.sh`
2. 保存输出结果
3. 查看 `QUICK_FIX.md` 获取更多解决方案
4. 参考 `docs/SERIAL_MANAGEMENT.md` 了解详细技术信息

### 🎯 预防措施

为避免类似问题，建议：

1. **使用按需启动方案**：最简单可靠
2. **定期检查服务状态**：`systemctl status n100-shutdown*`
3. **监控日志文件**：`tail -f /var/log/n100_shutdown.log`
4. **使用串口管理器**：自动处理冲突

---

**记住**：最重要的是确保电源控制功能正常工作。如果智能服务有问题，可以随时回退到手动管理模式。
