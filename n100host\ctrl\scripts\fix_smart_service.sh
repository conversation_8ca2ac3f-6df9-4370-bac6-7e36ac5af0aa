#!/bin/bash
# 修复智能关机服务的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户身份运行此脚本"
        exit 1
    fi
}

# 获取脚本所在目录
get_script_dir() {
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
    print_info "项目目录: $PROJECT_DIR"
}

# 停止现有服务
stop_services() {
    print_info "停止现有服务..."
    
    systemctl stop n100-shutdown-smart.service 2>/dev/null || true
    systemctl stop n100-shutdown.service 2>/dev/null || true
    
    print_info "服务已停止"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    mkdir -p /opt/n100/ctrl/src
    mkdir -p /opt/n100/ctrl/scripts
    mkdir -p /var/log
    mkdir -p /var/run
    
    print_info "目录创建完成"
}

# 复制文件
copy_files() {
    print_info "复制文件..."
    
    # 复制Python源文件
    cp "$PROJECT_DIR/src/n100_power_ctrl.py" /opt/n100/ctrl/src/
    cp "$PROJECT_DIR/src/n100_shutdown_daemon.py" /opt/n100/ctrl/src/
    cp "$PROJECT_DIR/src/shutdown_notify.py" /opt/n100/ctrl/src/
    cp "$PROJECT_DIR/src/serial_manager.py" /opt/n100/ctrl/src/
    
    # 复制脚本文件
    cp "$PROJECT_DIR/scripts/smart_shutdown_start.sh" /opt/n100/ctrl/scripts/
    cp "$PROJECT_DIR/scripts/smart_shutdown_stop.sh" /opt/n100/ctrl/scripts/
    cp "$PROJECT_DIR/scripts/n100-shutdown-hook.sh" /opt/n100/ctrl/scripts/
    
    # 设置执行权限
    chmod +x /opt/n100/ctrl/src/*.py
    chmod +x /opt/n100/ctrl/scripts/*.sh
    
    print_info "文件复制完成"
}

# 安装服务文件
install_service() {
    print_info "安装智能关机服务..."
    
    # 复制服务文件
    cp "$PROJECT_DIR/services/n100-shutdown-smart.service" /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_info "服务文件安装完成"
}

# 配置权限
configure_permissions() {
    print_info "配置权限..."
    
    # 检查串口设备
    if [ -e "/dev/ttyS4" ]; then
        chmod 666 /dev/ttyS4
        print_info "串口权限已配置"
    else
        print_warn "串口设备 /dev/ttyS4 不存在"
    fi
    
    # 创建日志文件
    touch /var/log/n100_shutdown.log
    chmod 644 /var/log/n100_shutdown.log
    
    print_info "权限配置完成"
}

# 测试服务
test_service() {
    print_info "测试智能关机服务..."
    
    # 启用服务
    systemctl enable n100-shutdown-smart.service
    
    # 启动服务
    if systemctl start n100-shutdown-smart.service; then
        print_info "智能关机服务启动成功"
        
        # 检查状态
        sleep 2
        if systemctl is-active --quiet n100-shutdown-smart.service; then
            print_info "智能关机服务运行正常"
        else
            print_warn "智能关机服务可能有问题，请检查日志"
        fi
    else
        print_error "智能关机服务启动失败"
        print_info "查看详细错误信息:"
        print_info "  systemctl status n100-shutdown-smart.service"
        print_info "  journalctl -xeu n100-shutdown-smart.service"
    fi
}

# 显示状态
show_status() {
    print_info "服务状态:"
    echo
    
    echo "智能关机服务状态:"
    systemctl status n100-shutdown-smart.service --no-pager -l || true
    echo
    
    echo "日志文件:"
    if [ -f "/var/log/n100_shutdown.log" ]; then
        echo "最近的日志条目:"
        tail -5 /var/log/n100_shutdown.log || true
    else
        echo "日志文件不存在"
    fi
}

# 主函数
main() {
    print_info "开始修复智能关机服务..."
    
    check_root
    get_script_dir
    stop_services
    create_directories
    copy_files
    install_service
    configure_permissions
    test_service
    
    print_info "智能关机服务修复完成!"
    echo
    show_status
    
    echo
    print_info "使用说明:"
    echo "1. 查看服务状态: systemctl status n100-shutdown-smart.service"
    echo "2. 查看日志: tail -f /var/log/n100_shutdown.log"
    echo "3. 重启服务: systemctl restart n100-shutdown-smart.service"
    echo "4. 停止服务: systemctl stop n100-shutdown-smart.service"
}

# 执行主函数
main "$@"
