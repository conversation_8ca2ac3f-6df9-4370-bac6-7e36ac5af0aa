# N100电源控制系统

## 📋 项目概述

N100电源控制系统是一个完整的电源管理解决方案，包含电源控制和智能关机两大功能模块：

- **电源控制模块**: 向电源板发送LED控制、呼吸灯设置等命令
- **关机管理模块**: 监听电源板关机请求，执行系统关机并发送确认

## 📁 目录结构

```
n100host/ctrl/
├── 📂 src/                          # 核心源代码
│   ├── n100_power_ctrl.py           # 电源控制器核心类
│   ├── power_ctrl_cli.py            # 命令行工具
│   ├── n100_shutdown_daemon.py      # 关机守护进程
│   └── shutdown_notify.py           # 关机通知脚本
├── 📂 scripts/                      # 安装和配置脚本
│   ├── install_shutdown_system.sh   # 关机系统安装脚本
│   └── n100-shutdown-hook.sh        # 关机钩子脚本
├── 📂 services/                     # 系统服务文件
│   └── n100-shutdown.service        # systemd服务配置
├── 📂 tests/                        # 测试文件
│   ├── test_shutdown_system.py      # 关机系统测试
│   └── test_syntax.py               # 语法测试
├── 📂 examples/                     # 示例代码
│   └── example.py                   # 使用示例
├── 📂 docs/                         # 文档说明
│   ├── README.md                    # 电源控制器说明
│   ├── USAGE.md                     # 使用指南
│   └── SHUTDOWN_SYSTEM.md           # 关机系统技术文档
├── 📄 README_SHUTDOWN.md            # 关机系统完整说明
└── 📄 Makefile                      # 便捷命令工具
```

## 🚀 快速开始

### 1. 电源控制功能

```bash
# 基本控制命令
python3 src/power_ctrl_cli.py led normal    # 设置LED正常模式
python3 src/power_ctrl_cli.py led breath    # 设置LED呼吸模式
python3 src/power_ctrl_cli.py breath 3      # 设置3秒呼吸周期
python3 src/power_ctrl_cli.py shutdown      # 发送关机成功消息

# 串口管理命令
python3 src/power_ctrl_cli.py check         # 检查串口状态
python3 src/power_ctrl_cli.py force-release # 强制释放串口锁

# 运行测试
python3 tests/test_syntax.py

# 查看示例
python3 examples/example.py
```

### 2. 关机系统安装

```bash
# 一键安装关机系统
sudo chmod +x scripts/install_shutdown_system.sh
sudo scripts/install_shutdown_system.sh

# 验证安装
systemctl status n100-shutdown.service
python tests/test_shutdown_system.py
```

## 🔧 核心功能

### 电源控制模块
- ✅ LED模式控制（正常/呼吸）
- ✅ 呼吸灯周期设置（1/3/5秒）
- ✅ 关机成功消息发送
- ✅ 自定义命令支持
- ✅ 自动重试和错误处理

### 关机管理模块
- ✅ 关机请求监听
- ✅ 自动ACK应答
- ✅ 系统关机执行
- ✅ 关机成功通知
- ✅ systemd服务集成

## 📡 通信协议

### 消息帧格式
```
| 帧头 | 长度 | 命令 | 数据 | 校验和 | 帧尾 |
| 0xAA | 1字节 | 1字节 | N字节 | 1字节 | 0x55 |
```

### 支持的命令
| 命令 | 代码 | 帧格式 | 说明 |
|------|------|--------|------|
| LED正常模式 | 0x01 | `AA 02 01 00 FF 55` | 设置LED正常模式 |
| LED呼吸模式 | 0x01 | `AA 02 01 01 FE 55` | 设置LED呼吸模式 |
| 1秒呼吸周期 | 0x02 | `AA 02 02 01 FD 55` | 设置1秒呼吸周期 |
| 3秒呼吸周期 | 0x02 | `AA 02 02 03 FB 55` | 设置3秒呼吸周期 |
| 5秒呼吸周期 | 0x02 | `AA 02 02 05 F9 55` | 设置5秒呼吸周期 |
| 关机成功 | 0x03 | `AA 01 03 FD 55` | 关机成功消息 |
| 关机请求 | 0x13 | `AA 01 13 ED 55` | 电源板发送的关机请求 |
| 通用应答 | 0x80 | `AA 01 80 80 55` | ACK应答帧 |

## 🛠️ 使用方法

### Python API
```python
from src.n100_power_ctrl import N100PowerController

# 创建控制器
controller = N100PowerController(port='/dev/ttyS4')

# 连接并控制
if controller.connect():
    controller.set_led_breath()         # 设置呼吸模式
    controller.set_breath_3s()          # 3秒周期
    controller.send_shutdown_success()  # 关机成功
    controller.disconnect()
```

### 命令行工具
```bash
# 使用相对路径
python src/power_ctrl_cli.py led breath
python src/power_ctrl_cli.py breath 3
python src/power_ctrl_cli.py shutdown

# 使用Makefile
make led-breath
make breath-3s
make shutdown
```

### 关机系统
```bash
# 安装关机系统
sudo scripts/install_shutdown_system.sh

# 管理服务
systemctl start n100-shutdown.service
systemctl status n100-shutdown.service
systemctl stop n100-shutdown.service

# 查看日志
tail -f /var/log/n100_shutdown.log
```

## 📊 测试验证

### 运行所有测试
```bash
# 语法和基本功能测试
python tests/test_syntax.py

# 关机系统测试
python tests/test_shutdown_system.py

# 使用示例测试
python examples/example.py
```

### 测试结果示例
```
测试结果: 5/5 个测试通过
🎉 所有测试通过！程序可以正常使用。
```

## 📖 详细文档

- **[README_SHUTDOWN.md](README_SHUTDOWN.md)** - 关机系统完整说明
- **[docs/README.md](docs/README.md)** - 电源控制器详细说明
- **[docs/USAGE.md](docs/USAGE.md)** - 使用指南
- **[docs/SHUTDOWN_SYSTEM.md](docs/SHUTDOWN_SYSTEM.md)** - 关机系统技术文档

## 🔧 配置要求

### 硬件要求
- N100主板
- 电源板（支持串口通信）
- 串口连接（ttyS4）

### 软件要求
- Ubuntu/Linux系统
- Python 3.6+
- pyserial库
- systemd（用于服务管理）

### 默认配置参数
- 串口设备: `/dev/ttyS4`
- 波特率: `115200`
- 超时时间: `1.0秒`
- 最大重试次数: `10次`（已从3次提升到10次以提高可靠性）

### 权限要求
- 串口访问权限
- root权限（用于关机操作）

## 🚨 故障排除

### 🔧 快速解决方案

#### 智能关机服务启动失败
```bash
# 运行诊断脚本
./scripts/diagnose.sh

# 或查看快速修复指南
cat QUICK_FIX.md

# 或查看详细故障排除
cat TROUBLESHOOTING.md
```

#### 串口冲突问题
```bash
# 检查串口状态
make check

# 停止关机服务（如果占用串口）
sudo systemctl stop n100-shutdown.service

# 强制释放串口锁
make force-release

# 发送控制命令
make led-breath
```

### 常见问题
1. **串口权限不足**: `sudo chmod 666 /dev/ttyS4`
2. **Python模块导入失败**: `pip install pyserial`
3. **服务启动失败**: `systemctl status n100-shutdown.service`
4. **串口被占用**: `python src/power_ctrl_cli.py check` 然后 `make force-release`

### 📖 详细文档
- **[QUICK_FIX.md](QUICK_FIX.md)** - 智能服务快速修复
- **[TROUBLESHOOTING.md](TROUBLESHOOTING.md)** - 完整故障排除指南
- **[docs/SERIAL_MANAGEMENT.md](docs/SERIAL_MANAGEMENT.md)** - 串口管理详解

### 调试模式
```bash
# 前台运行守护进程
python src/n100_shutdown_daemon.py --port /dev/ttyS4

# 手动测试关机通知
python src/shutdown_notify.py
```

## 📞 技术支持

如遇到问题，请提供：
1. 错误日志内容
2. 系统版本信息
3. 串口设备状态
4. 服务运行状态

## 📄 许可证

本项目遵循相关开源许可证。

## 🎉 更新日志

- **v1.0.0** - 初始版本，包含电源控制和关机管理功能
- 完整的协议实现
- systemd服务集成
- 完整的测试覆盖
- 详细的文档说明
