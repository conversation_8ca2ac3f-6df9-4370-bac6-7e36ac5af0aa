#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Power Controller Serial Communication Module
实现通过ttyS4发送消息帧，并等待通用应答帧的功能

支持的命令帧：
- 设置正常模式: AA 02 01 00 FF 55
- 设置呼吸模式: AA 02 01 01 FE 55
- 设置1秒周期:  AA 02 02 01 FD 55
- 设置3秒周期:  AA 02 02 03 FB 55
- 设置5秒周期:  AA 02 02 05 F9 55
- 关机成功:     AA 01 03 FD 55
- 通用应答:     AA 01 80 80 55
"""

import serial
import time
from typing import Optional, Tuple

# 协议常量
FRAME_HEADER = 0xAA    # 帧头
FRAME_TAIL = 0x55      # 帧尾
CMD_ACK = 0x80         # 通用应答命令

class PowerController:
    # 命令定义
    CMD_LED_MODE = 0x01        # LED模式设置命令
    CMD_BREATH_PERIOD = 0x02    # 呼吸灯周期设置命令
    CMD_SHUTDOWN = 0x03         # 关机成功消息
    CMD_ACK = 0x80              # 通用应答命令
    
    # LED模式定义
    LED_MODE_NORMAL = 0x00      # 正常模式
    LED_MODE_BREATH = 0x01      # 呼吸模式
    
    def __init__(self, port: str = '/dev/ttyS4', baudrate: int = 115200, timeout: float = 1.0):
        """
        初始化电源控制器串口通信
        
        参数:
            port (str): 串口设备路径 (默认: '/dev/ttyS4')
            baudrate (int): 波特率 (默认: 115200)
            timeout (float): 读写超时时间(秒) (默认: 1.0)
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial = None
    
    def connect(self) -> bool:
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout
            )
            print(f"已连接到 {self.port}，波特率 {self.baudrate}")
            return True
        except serial.SerialException as e:
            print(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            print("已断开串口连接")
    
    def calculate_checksum(self, data: bytes) -> int:
        """计算校验和（累加和）"""
        return sum(data) & 0xFF
    
    def create_frame(self, command: int, data: bytes = b'') -> bytes:
        """
        创建协议帧
        
        参数:
            command (int): 命令类型
            data (bytes): 数据内容
            
        返回:
            bytes: 完整的协议帧
        """
        if len(data) > 13:  # PROTOCOL_DATA_MAX
            raise ValueError("数据长度超过最大限制(13字节)")
            
        # 构建帧内容（不包含帧头和帧尾）
        frame_content = bytes([
            len(data) + 1,  # 数据长度 + 命令长度
            command
        ]) + data
        
        # 计算校验和（长度+命令+数据）
        checksum = self.calculate_checksum(frame_content)
        
        # 组装完整帧
        frame = bytes([FRAME_HEADER]) + frame_content + bytes([checksum, FRAME_TAIL])
        return frame
    
    def parse_frame(self, frame: bytes) -> Tuple[bool, int, bytes]:
        """
        解析接收到的帧
        
        参数:
            frame (bytes): 接收到的完整帧数据
            
        返回:
            Tuple[bool, int, bytes]: (是否有效, 命令类型, 数据内容)
        """
        if len(frame) < 5:  # 最小帧长度
            return False, 0, b''
            
        if frame[0] != FRAME_HEADER or frame[-1] != FRAME_TAIL:
            return False, 0, b''
            
        length = frame[1]
        if len(frame) != length + 4:  # 头(1) + 长度(1) + 命令(1) + 数据(n) + 校验(1) + 尾(1)
            return False, 0, b''
            
        # 提取命令和数据
        command = frame[2]
        data = frame[3:-2]  # 去掉头、长度、命令、校验和、尾
        
        # 验证校验和
        expected_checksum = frame[-2]
        actual_checksum = self.calculate_checksum(frame[1:-2])  # 计算长度+命令+数据的校验和
        
        if expected_checksum != actual_checksum:
            return False, 0, b''
            
        return True, command, data
    
    def set_led_mode(self, mode: int, max_retries: int = 3) -> bool:
        """
        设置LED模式
        
        参数:
            mode (int): LED模式 (LED_MODE_NORMAL 或 LED_MODE_BREATH)
            max_retries (int): 最大重试次数
            
        返回:
            bool: 是否成功设置并收到确认
        """
        return self._send_command(self.CMD_LED_MODE, bytes([mode]), max_retries)
    
    def set_breath_period(self, seconds: int, max_retries: int = 3) -> bool:
        """
        设置呼吸灯周期
        
        参数:
            seconds (int): 周期秒数 (1/3/5)
            max_retries (int): 最大重试次数
            
        返回:
            bool: 是否成功设置并收到确认
        """
        if seconds not in [1, 3, 5]:
            print(f"错误: 不支持的呼吸周期 {seconds}秒，请使用1/3/5秒")
            return False
        return self._send_command(self.CMD_BREATH_PERIOD, bytes([seconds]), max_retries)
    
    def send_shutdown_success(self, max_retries: int = 3) -> bool:
        """
        发送关机成功消息
        
        参数:
            max_retries (int): 最大重试次数
            
        返回:
            bool: 是否成功发送并收到确认
        """
        # 关机成功消息不需要等待ACK，直接发送
        return self._send_command(self.CMD_SHUTDOWN, b'', max_retries, wait_ack=False)
    
    def _send_command(self, command: int, data: bytes = b'', max_retries: int = 3, retry_interval: float = 0.5, wait_ack: bool = True) -> bool:
        """
        发送命令并等待确认
        
        参数:
            command (int): 命令字节
            data (bytes): 数据字节
            max_retries (int): 最大重试次数
            retry_interval (float): 重试间隔（秒）
            wait_ack (bool): 是否等待ACK
        
        返回:
            bool: 是否成功
        """
        if not hasattr(self, 'serial') or not self.serial.is_open:
            print("[错误] 串口未连接")
            return False
        
        # 清空输入缓冲区
        self.serial.reset_input_buffer()
        
        # 创建帧
        frame = self.create_frame(command, data)
        
        for attempt in range(max_retries):
            try:
                # 发送帧
                print(f"[发送] 帧: {frame.hex(' ').upper()}")
                self.serial.write(frame)
                self.serial.flush()
                
                if not wait_ack:
                    return True
                
                # 等待响应
                print("[接收] 等待响应...")
                start_time = time.time()
                last_activity_time = start_time
                
                while time.time() - start_time < self.timeout:
                    # 检查是否有数据可读
                    if self.serial.in_waiting > 0:
                        last_activity_time = time.time()
                        # 读取所有可用数据
                        raw_data = self.serial.read(self.serial.in_waiting)
                        print(f"[接收] 原始数据: {raw_data.hex(' ').upper()}")
                        
                        # 处理每个字节
                        for byte in raw_data:
                            result = self._process_received_byte(byte)
                            if result is not None:
                                valid, resp_command, resp_data = result
                                print(f"[调试] 处理结果: 有效={valid}, 命令={resp_command:02X}, 数据={resp_data.hex(' ').upper() if resp_data else '无'}")
                                
                                # 检查是否为通用应答帧，即使校验和不正确也接受
                                if resp_command == self.CMD_ACK:
                                    print(f"[接收] 确认帧: 命令={resp_command:02X}")
                                    return True
                
                # 如果超过1秒没有活动，打印等待状态
                if time.time() - last_activity_time > 1.0:
                    last_activity_time = time.time()
                    elapsed = time.time() - start_time
                    print(f"[接收] 等待响应中... 已等待 {elapsed:.1f} 秒")
                
                # 避免CPU占用过高
                time.sleep(0.01)
            
            print(f"[超时] 未收到确认，{retry_interval}秒后重试... ({attempt + 1}/{max_retries})")
            time.sleep(retry_interval)
            
        except Exception as e:
            print(f"[错误] 发送/接收错误: {e}")
            time.sleep(retry_interval)
    
    if wait_ack:
        print(f"[错误] 在{max_retries}次尝试后未收到确认")
    return not wait_ack  # 如果不等待ACK，则发送成功即返回True
    
    def _process_received_byte(self, byte: int) -> Optional[Tuple[bool, int, bytes]]:
        """
        处理接收到的单个字节（用于实时解析）
        
        参数:
            byte (int): 接收到的字节
            
        返回:
            Optional[Tuple[bool, int, bytes]]: 如果解析到完整帧，返回(是否有效, 命令, 数据)，否则返回None
        """
        # 初始化接收缓冲区
        if not hasattr(self, '_rx_buffer'):
            self._rx_buffer = bytearray()
            self._rx_state = 0  # 0: 等待帧头, 1: 等待剩余数据
            self._rx_frame = bytearray()
        
        # 添加到缓冲区
        self._rx_buffer.append(byte)
        
        # 处理缓冲区中的数据
        while len(self._rx_buffer) > 0:
            if self._rx_state == 0:  # 等待帧头
                # 查找帧头
                try:
                    header_pos = self._rx_buffer.index(FRAME_HEADER)
                    # 如果帧头不在第一个位置，丢弃前面的数据
                    if header_pos > 0:
                        print(f"[调试] 丢弃 {header_pos} 字节无效数据: {self._rx_buffer[:header_pos].hex(' ').upper()}")
                        self._rx_buffer = self._rx_buffer[header_pos:]
                        continue
                    
                    # 找到帧头，开始新帧
                    self._rx_frame = bytearray([self._rx_buffer[0]])
                    self._rx_buffer = self._rx_buffer[1:]
                    self._rx_state = 1
                    
                except ValueError:
                    # 没有找到帧头，清空缓冲区
                    if len(self._rx_buffer) > 0:
                        print(f"[调试] 未找到帧头，丢弃 {len(self._rx_buffer)} 字节: {self._rx_buffer.hex(' ').upper()}")
                        self._rx_buffer.clear()
                    return None
            
            elif self._rx_state == 1:  # 等待剩余数据
                # 检查是否已收到足够的字节（至少2个：长度和命令）
                if len(self._rx_frame) == 1 and len(self._rx_buffer) >= 2:
                    # 获取长度和命令
                    self._rx_frame.extend(self._rx_buffer[:2])
                    self._rx_buffer = self._rx_buffer[2:]
                    
                    length = self._rx_frame[1]  # 数据长度
                    
                    # 检查长度是否有效
                    if length > 13:  # 最大数据长度为13
                        print(f"[调试] 无效长度: {length}")
                        self._rx_state = 0
                        self._rx_frame.clear()
                        continue
                    
                    # 计算完整帧长度：帧头(1) + 长度(1) + 命令(1) + 数据(n) + 校验和(1) + 帧尾(1)
                    total_length = 3 + length + 1 + 1
                    
                    # 检查是否已收到完整帧
                    if len(self._rx_frame) + len(self._rx_buffer) >= total_length:
                        # 提取剩余数据
                        needed = total_length - len(self._rx_frame)
                        self._rx_frame.extend(self._rx_buffer[:needed])
                        self._rx_buffer = self._rx_buffer[needed:]
                        
                        # 验证帧尾
                        if self._rx_frame[-1] != FRAME_TAIL:
                            print(f"[调试] 无效帧尾: {self._rx_frame.hex(' ').upper()}")
                            self._rx_state = 0
                            self._rx_frame.clear()
                            continue
                        
                        # 提取帧内容（去掉帧头和帧尾）
                        frame_data = self._rx_frame[1:-1]
                        
                        # 计算校验和
                        checksum = sum(frame_data[:-1]) & 0xFF
                        
                        # 提取命令和数据
                        command = frame_data[1]  # 第一个字节是长度，第二个是命令
                        data = frame_data[2:-1]  # 数据在命令和校验和之间
                        
                        # 打印调试信息
                        print(f"[调试] 解析到完整帧: {self._rx_frame.hex(' ').upper()}")
                        print(f"[调试] 长度: {frame_data[0]}, 命令: {command:02X}, 数据: {data.hex(' ').upper() if data else '无'}")
                        print(f"[调试] 计算校验和: {checksum:02X}, 接收校验和: {frame_data[-1]:02X}, 校验和: {'有效' if checksum == frame_data[-1] else '无效'}")
                        
                        # 重置状态
                        self._rx_state = 0
                        
                        # 对于ACK帧，即使校验和错误也返回True，因为设备可能发送了错误的校验和
                        if command == self.CMD_ACK:
                            print(f"[调试] 检测到ACK帧，强制返回成功")
                            return (True, command, data)
                            
                        # 返回解析结果
                        return (checksum == frame_data[-1], command, data)
                    
                    # 如果缓冲区中没有足够的数据，等待更多数据
                    return None
                
                # 如果没有足够的数据，等待更多数据
                if len(self._rx_buffer) == 0:
                    return None
                
                # 添加一个字节到当前帧
                self._rx_frame.append(self._rx_buffer[0])
                self._rx_buffer = self._rx_buffer[1:]
        
        return None
    
    def read_response(self, timeout: float = 1.0) -> Optional[Tuple[bool, int, bytes]]:
        """
        读取并解析响应帧
        
        参数:
            timeout (float): 读取超时(秒)
            
        返回:
            Optional[Tuple[bool, int, bytes]]: (是否有效, 命令类型, 数据内容) 或 None（如果超时）
        """
        if not self.serial or not self.serial.is_open:
            return None
            
        start_time = time.time()
        buffer = bytearray()
        
        while time.time() - start_time < timeout:
            # 读取可用数据
            if self.serial.in_waiting > 0:
                buffer.extend(self.serial.read(self.serial.in_waiting))
                
                # 尝试解析帧
                while len(buffer) >= 5:  # 最小帧长度
                    # 查找帧头
                    try:
                        header_pos = buffer.index(FRAME_HEADER)
                        if header_pos > 0:
                            print(f"丢弃 {header_pos} 字节无效数据: {buffer[:header_pos].hex(' ')}")
                            buffer = buffer[header_pos:]
                            
                        if len(buffer) < 5:  # 不足最小帧长度
                            break
                            
                        # 获取帧长度
                        frame_len = buffer[1] + 4  # 头(1) + 长度(1) + 命令(1) + 数据(n) + 校验(1) + 尾(1)
                        
                        if len(buffer) < frame_len:
                            # 帧不完整，等待更多数据
                            break
                            
                        # 提取完整帧
                        frame = buffer[:frame_len]
                        buffer = buffer[frame_len:]
                        
                        # 解析帧
                        valid, command, data = self.parse_frame(frame)
                        if valid:
                            return True, command, data
                        else:
                            print(f"收到无效帧: {frame.hex(' ')}")
                            
                    except ValueError:
                        # 没有找到帧头，清空缓冲区
                        print(f"丢弃 {len(buffer)} 字节无效数据: {buffer.hex(' ')}")
                        buffer.clear()
                        break
                        
            # 短暂休眠以避免CPU占用过高
            time.sleep(0.01)
            
        return None

def main():
    import argparse
    
    # 创建解析器
    parser = argparse.ArgumentParser(description='电源控制器串口通信工具')
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 通用参数
    parser.add_argument('--port', type=str, default='/dev/ttyS4', help='串口设备路径')
    parser.add_argument('--baudrate', type=int, default=115200, help='波特率')
    parser.add_argument('--retries', type=int, default=15, help='最大重试次数')
    
    # 设置LED模式
    mode_parser = subparsers.add_parser('set_led_mode', help='设置LED模式')
    mode_parser.add_argument('mode', choices=['normal', 'breath'], help='LED模式')
    
    # 设置呼吸周期
    period_parser = subparsers.add_parser('set_breath_period', help='设置呼吸灯周期')
    period_parser.add_argument('seconds', type=int, choices=[1, 3, 5], help='呼吸周期(秒)')
    
    # 发送关机成功
    shutdown_parser = subparsers.add_parser('shutdown', help='发送关机成功消息')
    
    # 自定义命令
    custom_parser = subparsers.add_parser('custom', help='发送自定义命令')
    custom_parser.add_argument('command_hex', type=str, help='命令(16进制)')
    custom_parser.add_argument('data_hex', type=str, nargs='?', default='', help='数据(16进制, 可选)')
    
    args = parser.parse_args()
    
    # 创建并连接串口
    controller = PowerController(port=args.port, baudrate=args.baudrate)
    if not controller.connect():
        return 1
    
    try:
        success = False
        
        if args.command == 'set_led_mode':
            mode = controller.LED_MODE_NORMAL if args.mode == 'normal' else controller.LED_MODE_BREATH
            success = controller.set_led_mode(mode, args.retries)
            
        elif args.command == 'set_breath_period':
            success = controller.set_breath_period(args.seconds, args.retries)
            
        elif args.command == 'shutdown':
            success = controller.send_shutdown_success(args.retries)
            
        elif args.command == 'custom':
            try:
                cmd = int(args.command_hex, 16)
                data = bytes.fromhex(args.data_hex) if args.data_hex else b''
                success = controller._send_command(cmd, data, args.retries)
            except ValueError as e:
                print(f"错误: 无效的命令或数据格式 - {e}")
                return 1
                
        else:
            parser.print_help()
            return 0
        
        if success:
            print("操作成功完成")
            return 0
        else:
            print("操作失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断")
        return 1
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()


