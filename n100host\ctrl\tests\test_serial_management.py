#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口管理测试脚本
测试串口锁机制和管理功能（不需要实际的串口设备）
"""

import os
import sys
import time
import tempfile
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))


def test_serial_lock_mechanism():
    """测试串口锁机制"""
    print("=== 测试串口锁机制 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from serial_manager import SerialManager
            
            # 使用临时目录作为锁文件位置
            with tempfile.TemporaryDirectory() as temp_dir:
                lock_file = os.path.join(temp_dir, "test_serial.lock")
                
                # 修改锁文件路径
                original_lock_file = SerialManager.LOCK_FILE
                SerialManager.LOCK_FILE = lock_file
                
                try:
                    # 测试获取锁
                    manager1 = SerialManager('/dev/ttyS4')
                    success1 = manager1.acquire_lock(timeout=1.0)
                    
                    if success1:
                        print("✓ 第一个管理器成功获取锁")
                        
                        # 测试第二个管理器无法获取锁
                        manager2 = SerialManager('/dev/ttyS4')
                        success2 = manager2.acquire_lock(timeout=1.0)
                        
                        if not success2:
                            print("✓ 第二个管理器正确被阻止获取锁")
                            
                            # 释放第一个锁
                            manager1.release_lock()
                            print("✓ 第一个管理器成功释放锁")
                            
                            # 第二个管理器现在应该能获取锁
                            success3 = manager2.acquire_lock(timeout=1.0)
                            if success3:
                                print("✓ 第二个管理器在锁释放后成功获取锁")
                                manager2.release_lock()
                                return True
                            else:
                                print("✗ 第二个管理器在锁释放后仍无法获取锁")
                        else:
                            print("✗ 第二个管理器错误地获取了锁")
                    else:
                        print("✗ 第一个管理器无法获取锁")
                        
                finally:
                    # 恢复原始锁文件路径
                    SerialManager.LOCK_FILE = original_lock_file
                    
        except ImportError as e:
            print(f"✗ 无法导入串口管理器: {e}")
            return False
    
    return False


def test_lock_file_cleanup():
    """测试锁文件清理功能"""
    print("\n=== 测试锁文件清理功能 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from serial_manager import check_serial_status, force_release_lock, SerialManager
            
            # 使用临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                lock_file = os.path.join(temp_dir, "test_serial.lock")
                
                # 修改锁文件路径
                original_lock_file = SerialManager.LOCK_FILE
                SerialManager.LOCK_FILE = lock_file
                
                try:
                    # 创建一个僵尸锁文件（进程不存在）
                    with open(lock_file, 'w') as f:
                        f.write("99999\n")  # 不存在的PID
                        f.write(f"{time.time()}\n")
                    
                    print("创建了僵尸锁文件")
                    
                    # 检查状态应该清理僵尸锁
                    status = check_serial_status()
                    if status:
                        print("✓ 僵尸锁文件被正确清理")
                    else:
                        print("✗ 僵尸锁文件未被清理")
                        return False
                    
                    # 测试强制释放
                    with open(lock_file, 'w') as f:
                        f.write(f"{os.getpid()}\n")
                        f.write(f"{time.time()}\n")
                    
                    success = force_release_lock()
                    if success and not os.path.exists(lock_file):
                        print("✓ 强制释放锁文件成功")
                        return True
                    else:
                        print("✗ 强制释放锁文件失败")
                        return False
                        
                finally:
                    # 恢复原始锁文件路径
                    SerialManager.LOCK_FILE = original_lock_file
                    
        except ImportError as e:
            print(f"✗ 无法导入串口管理器: {e}")
            return False
    
    return False


def test_context_manager():
    """测试上下文管理器"""
    print("\n=== 测试上下文管理器 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial_class = MagicMock()
    mock_serial_instance = MagicMock()
    mock_serial_class.return_value = mock_serial_instance
    mock_serial.Serial = mock_serial_class
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from serial_manager import SerialManager
            
            # 使用临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                lock_file = os.path.join(temp_dir, "test_serial.lock")
                
                # 修改锁文件路径
                original_lock_file = SerialManager.LOCK_FILE
                SerialManager.LOCK_FILE = lock_file
                
                try:
                    # 模拟连接成功
                    mock_serial_instance.is_open = True
                    
                    # 测试上下文管理器
                    try:
                        with SerialManager('/dev/ttyS4') as manager:
                            print("✓ 成功进入上下文管理器")
                            
                            # 检查锁文件是否存在
                            if os.path.exists(lock_file):
                                print("✓ 锁文件已创建")
                            else:
                                print("✗ 锁文件未创建")
                                return False
                            
                            # 检查控制器是否已连接
                            if manager.controller:
                                print("✓ 控制器已连接")
                            else:
                                print("✗ 控制器未连接")
                                return False
                        
                        # 退出上下文后检查锁文件是否被清理
                        if not os.path.exists(lock_file):
                            print("✓ 退出上下文后锁文件已清理")
                            return True
                        else:
                            print("✗ 退出上下文后锁文件未清理")
                            return False
                            
                    except Exception as e:
                        print(f"✗ 上下文管理器测试失败: {e}")
                        return False
                        
                finally:
                    # 恢复原始锁文件路径
                    SerialManager.LOCK_FILE = original_lock_file
                    
        except ImportError as e:
            print(f"✗ 无法导入串口管理器: {e}")
            return False
    
    return False


def main():
    """主测试函数"""
    print("串口管理系统测试")
    print("=" * 50)
    
    tests = [
        ("串口锁机制", test_serial_lock_mechanism),
        ("锁文件清理", test_lock_file_cleanup),
        ("上下文管理器", test_context_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！串口管理系统可以正常使用。")
        return 0
    else:
        print("❌ 部分测试失败，请检查代码。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
