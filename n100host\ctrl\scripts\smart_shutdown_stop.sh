#!/bin/bash
# N100智能关机停止脚本

LOG_FILE="/var/log/n100_shutdown.log"
PID_FILE="/var/run/n100_shutdown.pid"

# 日志函数
log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 停止关机守护进程
stop_shutdown_daemon() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        
        if kill -0 "$PID" 2>/dev/null; then
            log_msg "停止关机守护进程，PID: $PID"
            kill -TERM "$PID"
            
            # 等待进程结束
            for i in {1..10}; do
                if ! kill -0 "$PID" 2>/dev/null; then
                    break
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制杀死
            if kill -0 "$PID" 2>/dev/null; then
                log_msg "强制停止关机守护进程"
                kill -KILL "$PID"
            fi
        fi
        
        rm -f "$PID_FILE"
        log_msg "关机守护进程已停止"
    else
        log_msg "未找到关机守护进程PID文件"
    fi
}

# 清理串口锁
cleanup_serial_lock() {
    python3 /opt/n100/ctrl/src/serial_manager.py --force-release > /dev/null 2>&1
    log_msg "已清理串口锁"
}

# 主逻辑
main() {
    log_msg "智能关机服务停止..."
    
    stop_shutdown_daemon
    cleanup_serial_lock
    
    log_msg "智能关机服务已停止"
}

# 执行主函数
main
