#!/bin/bash
# N100关机系统诊断脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_ok() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# 检查系统信息
check_system() {
    print_header "系统信息"
    
    echo "操作系统: $(uname -a)"
    echo "Python版本: $(python3 --version 2>/dev/null || echo '未安装')"
    echo "当前用户: $(whoami)"
    echo "当前目录: $(pwd)"
    echo
}

# 检查文件结构
check_file_structure() {
    print_header "文件结构检查"
    
    local project_dir="$(pwd)"
    
    # 检查主要目录
    local dirs=("src" "scripts" "services" "tests" "docs" "examples")
    for dir in "${dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_ok "目录存在: $dir"
        else
            print_error "目录缺失: $dir"
        fi
    done
    
    echo
    
    # 检查关键文件
    local files=(
        "src/n100_power_ctrl.py"
        "src/n100_shutdown_daemon.py"
        "src/serial_manager.py"
        "scripts/smart_shutdown_start.sh"
        "scripts/smart_shutdown_stop.sh"
        "services/n100-shutdown-smart.service"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_ok "文件存在: $file"
        else
            print_error "文件缺失: $file"
        fi
    done
    
    echo
}

# 检查安装状态
check_installation() {
    print_header "安装状态检查"
    
    # 检查 /opt/n100/ctrl 目录
    if [ -d "/opt/n100/ctrl" ]; then
        print_ok "安装目录存在: /opt/n100/ctrl"
        
        # 检查子目录
        if [ -d "/opt/n100/ctrl/src" ]; then
            print_ok "源码目录存在"
        else
            print_error "源码目录缺失: /opt/n100/ctrl/src"
        fi
        
        if [ -d "/opt/n100/ctrl/scripts" ]; then
            print_ok "脚本目录存在"
        else
            print_error "脚本目录缺失: /opt/n100/ctrl/scripts"
        fi
    else
        print_error "安装目录不存在: /opt/n100/ctrl"
    fi
    
    # 检查服务文件
    if [ -f "/etc/systemd/system/n100-shutdown-smart.service" ]; then
        print_ok "智能服务文件已安装"
    else
        print_warn "智能服务文件未安装"
    fi
    
    if [ -f "/etc/systemd/system/n100-shutdown.service" ]; then
        print_ok "标准服务文件已安装"
    else
        print_warn "标准服务文件未安装"
    fi
    
    echo
}

# 检查服务状态
check_services() {
    print_header "服务状态检查"
    
    # 检查智能关机服务
    if systemctl list-unit-files | grep -q "n100-shutdown-smart.service"; then
        local status=$(systemctl is-active n100-shutdown-smart.service 2>/dev/null)
        local enabled=$(systemctl is-enabled n100-shutdown-smart.service 2>/dev/null)
        
        echo "智能关机服务:"
        echo "  状态: $status"
        echo "  启用: $enabled"
        
        if [ "$status" = "active" ]; then
            print_ok "智能关机服务运行正常"
        elif [ "$status" = "failed" ]; then
            print_error "智能关机服务运行失败"
        else
            print_warn "智能关机服务状态: $status"
        fi
    else
        print_warn "智能关机服务未安装"
    fi
    
    # 检查标准关机服务
    if systemctl list-unit-files | grep -q "n100-shutdown.service"; then
        local status=$(systemctl is-active n100-shutdown.service 2>/dev/null)
        local enabled=$(systemctl is-enabled n100-shutdown.service 2>/dev/null)
        
        echo "标准关机服务:"
        echo "  状态: $status"
        echo "  启用: $enabled"
        
        if [ "$status" = "active" ]; then
            print_warn "标准关机服务正在运行（可能占用串口）"
        else
            print_ok "标准关机服务未运行"
        fi
    else
        print_warn "标准关机服务未安装"
    fi
    
    echo
}

# 检查串口状态
check_serial() {
    print_header "串口状态检查"
    
    # 检查串口设备
    if [ -e "/dev/ttyS4" ]; then
        print_ok "串口设备存在: /dev/ttyS4"
        
        # 检查权限
        local perms=$(ls -la /dev/ttyS4 | awk '{print $1}')
        echo "  权限: $perms"
        
        if [[ "$perms" == *"rw-rw-rw-"* ]] || [[ "$perms" == *"rw-rw----"* ]]; then
            print_ok "串口权限正常"
        else
            print_warn "串口权限可能有问题"
        fi
    else
        print_warn "串口设备不存在: /dev/ttyS4 (在虚拟环境中这是正常的)"
    fi
    
    # 检查串口占用
    local tty_processes=$(pgrep -f "ttyS4" 2>/dev/null)
    if [ -n "$tty_processes" ]; then
        print_warn "发现使用ttyS4的进程:"
        ps -p $tty_processes -o pid,ppid,cmd 2>/dev/null || true
    else
        print_ok "没有进程占用ttyS4"
    fi
    
    echo
}

# 检查Python依赖
check_dependencies() {
    print_header "Python依赖检查"
    
    # 检查Python3
    if command -v python3 >/dev/null 2>&1; then
        print_ok "Python3 已安装: $(python3 --version)"
    else
        print_error "Python3 未安装"
    fi
    
    # 检查pip3
    if command -v pip3 >/dev/null 2>&1; then
        print_ok "pip3 已安装"
    else
        print_warn "pip3 未安装"
    fi
    
    # 检查pyserial
    if python3 -c "import serial" 2>/dev/null; then
        print_ok "pyserial 已安装"
    else
        print_error "pyserial 未安装 (运行: pip3 install pyserial)"
    fi
    
    echo
}

# 检查日志
check_logs() {
    print_header "日志检查"
    
    # 检查日志文件
    if [ -f "/var/log/n100_shutdown.log" ]; then
        print_ok "日志文件存在: /var/log/n100_shutdown.log"
        
        local log_size=$(stat -c%s "/var/log/n100_shutdown.log" 2>/dev/null || echo "0")
        echo "  文件大小: $log_size 字节"
        
        if [ "$log_size" -gt 0 ]; then
            echo "  最近的日志条目:"
            tail -3 /var/log/n100_shutdown.log 2>/dev/null | sed 's/^/    /'
        else
            print_warn "日志文件为空"
        fi
    else
        print_warn "日志文件不存在: /var/log/n100_shutdown.log"
    fi
    
    echo
}

# 提供修复建议
provide_suggestions() {
    print_header "修复建议"
    
    echo "根据诊断结果，建议采取以下措施:"
    echo
    
    # 检查是否需要安装
    if [ ! -d "/opt/n100/ctrl" ]; then
        print_info "1. 运行安装脚本:"
        echo "   sudo scripts/install_shutdown_system.sh"
        echo
    fi
    
    # 检查是否需要修复
    if [ -d "/opt/n100/ctrl" ] && [ ! -f "/opt/n100/ctrl/scripts/smart_shutdown_start.sh" ]; then
        print_info "2. 运行修复脚本:"
        echo "   sudo scripts/fix_smart_service.sh"
        echo
    fi
    
    # 检查Python依赖
    if ! python3 -c "import serial" 2>/dev/null; then
        print_info "3. 安装Python依赖:"
        echo "   pip3 install pyserial"
        echo
    fi
    
    # 检查服务状态
    local smart_status=$(systemctl is-active n100-shutdown-smart.service 2>/dev/null)
    if [ "$smart_status" = "failed" ]; then
        print_info "4. 重启智能关机服务:"
        echo "   sudo systemctl restart n100-shutdown-smart.service"
        echo "   systemctl status n100-shutdown-smart.service"
        echo
    fi
    
    print_info "5. 查看详细文档:"
    echo "   cat QUICK_FIX.md"
    echo "   cat docs/SERIAL_MANAGEMENT.md"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════╗"
    echo "║        N100关机系统诊断工具          ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
    echo
    
    check_system
    check_file_structure
    check_installation
    check_services
    check_serial
    check_dependencies
    check_logs
    provide_suggestions
    
    echo -e "${BLUE}诊断完成！${NC}"
}

# 执行主函数
main "$@"
