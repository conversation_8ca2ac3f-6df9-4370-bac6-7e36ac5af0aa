#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器命令行工具
用于通过命令行控制电源板
"""

import argparse
import sys
import time
from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod


def on_ack_received(command):
    """ACK接收回调函数"""
    print(f"[回调] 收到ACK应答，命令: 0x{command:02X}")


def on_error(error_msg):
    """错误回调函数"""
    print(f"[回调] 错误: {error_msg}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='N100电源控制器命令行工具')
    
    # 串口参数
    parser.add_argument('--port', type=str, default='/dev/ttyS4', 
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--baudrate', type=int, default=115200, 
                       help='波特率 (默认: 115200)')
    parser.add_argument('--timeout', type=float, default=1.0, 
                       help='读取超时时间(秒) (默认: 1.0)')
    parser.add_argument('--retries', type=int, default=3, 
                       help='最大重试次数 (默认: 3)')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # LED模式命令
    led_parser = subparsers.add_parser('led', help='设置LED模式')
    led_parser.add_argument('mode', choices=['normal', 'breath'], 
                           help='LED模式: normal(正常) 或 breath(呼吸)')
    
    # 呼吸周期命令
    breath_parser = subparsers.add_parser('breath', help='设置呼吸灯周期')
    breath_parser.add_argument('period', type=int, choices=[1, 3, 5], 
                              help='呼吸周期(秒): 1, 3, 或 5')
    
    # 关机成功命令
    shutdown_parser = subparsers.add_parser('shutdown', help='发送关机成功消息')
    
    # 自定义命令
    custom_parser = subparsers.add_parser('custom', help='发送自定义命令')
    custom_parser.add_argument('command_hex', type=str, 
                              help='命令字节(16进制，如: 01)')
    custom_parser.add_argument('data_hex', type=str, nargs='?', default='', 
                              help='数据字节(16进制，如: 0001，可选)')
    
    # 测试命令
    test_parser = subparsers.add_parser('test', help='运行测试序列')
    test_parser.add_argument('--delay', type=float, default=2.0, 
                            help='命令间延迟时间(秒) (默认: 2.0)')
    
    # 状态查询命令
    status_parser = subparsers.add_parser('status', help='查询串口状态')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 创建控制器实例
    controller = N100PowerController(
        port=args.port,
        baudrate=args.baudrate,
        timeout=args.timeout,
        max_retries=args.retries
    )
    
    # 设置回调函数
    controller.set_ack_callback(on_ack_received)
    controller.set_error_callback(on_error)
    
    # 连接串口
    if not controller.connect():
        print("错误: 无法连接到串口")
        return 1
    
    try:
        success = False
        
        if args.command == 'led':
            # 设置LED模式
            mode = LEDMode.NORMAL if args.mode == 'normal' else LEDMode.BREATH
            success = controller.set_led_mode(mode)
            
        elif args.command == 'breath':
            # 设置呼吸周期
            period = BreathPeriod(args.period)
            success = controller.set_breath_period(period)
            
        elif args.command == 'shutdown':
            # 发送关机成功
            success = controller.send_shutdown_success()
            
        elif args.command == 'custom':
            # 发送自定义命令
            try:
                cmd = int(args.command_hex, 16)
                data = bytes.fromhex(args.data_hex) if args.data_hex else b''
                success = controller.send_custom_command(cmd, data)
            except ValueError as e:
                print(f"错误: 无效的16进制格式 - {e}")
                return 1
                
        elif args.command == 'test':
            # 运行测试序列
            print("开始运行测试序列...")
            
            test_commands = [
                ("设置LED正常模式", lambda: controller.set_led_normal()),
                ("设置LED呼吸模式", lambda: controller.set_led_breath()),
                ("设置呼吸周期1秒", lambda: controller.set_breath_1s()),
                ("设置呼吸周期3秒", lambda: controller.set_breath_3s()),
                ("设置呼吸周期5秒", lambda: controller.set_breath_5s()),
                ("发送关机成功", lambda: controller.send_shutdown_success()),
            ]
            
            success_count = 0
            for desc, cmd_func in test_commands:
                print(f"\n执行: {desc}")
                if cmd_func():
                    success_count += 1
                    print(f"✓ {desc} - 成功")
                else:
                    print(f"✗ {desc} - 失败")
                
                if args.delay > 0:
                    time.sleep(args.delay)
            
            print(f"\n测试完成: {success_count}/{len(test_commands)} 个命令成功")
            success = success_count == len(test_commands)
            
        elif args.command == 'status':
            # 查询状态
            info = controller.get_port_info()
            print("串口状态信息:")
            for key, value in info.items():
                print(f"  {key}: {value}")
            success = True
        
        # 输出结果
        if success:
            print("\n操作成功完成")
            return 0
        else:
            print("\n操作失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return 1
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        return 1
    finally:
        controller.disconnect()


if __name__ == "__main__":
    sys.exit(main())
