# N100电源控制器 Makefile

.PHONY: help test example install clean led-normal led-breath breath-1s breath-3s breath-5s shutdown status

# 默认目标
help:
	@echo "N100电源控制系统 - 可用命令:"
	@echo ""
	@echo "测试命令:"
	@echo "  make test           - 运行语法测试"
	@echo "  make test-shutdown  - 运行关机系统测试"
	@echo "  make test-all       - 运行完整测试序列"
	@echo "  make example        - 运行使用示例"
	@echo ""
	@echo "电源控制命令:"
	@echo "  make led-normal     - 设置LED正常模式"
	@echo "  make led-breath     - 设置LED呼吸模式"
	@echo "  make breath-1s      - 设置1秒呼吸周期"
	@echo "  make breath-3s      - 设置3秒呼吸周期"
	@echo "  make breath-5s      - 设置5秒呼吸周期"
	@echo "  make shutdown       - 发送关机成功消息"
	@echo "  make status         - 查询串口状态"
	@echo ""
	@echo "关机系统管理:"
	@echo "  make install-shutdown - 安装关机系统"
	@echo "  make service-status   - 查看服务状态"
	@echo "  make service-start    - 启动关机服务"
	@echo "  make service-stop     - 停止关机服务"
	@echo "  make service-restart  - 重启关机服务"
	@echo "  make logs            - 查看关机日志"
	@echo "  make service-logs    - 查看服务日志"
	@echo ""
	@echo "串口管理:"
	@echo "  make check-serial     - 检查串口状态"
	@echo "  make release-serial   - 强制释放串口锁"
	@echo "  make test-serial      - 测试串口管理器"
	@echo ""
	@echo "其他命令:"
	@echo "  make install        - 安装依赖"
	@echo "  make clean          - 清理临时文件"

# 安装依赖
install:
	pip install pyserial

# 运行语法测试
test:
	python tests/test_syntax.py

# 运行使用示例
example:
	python examples/example.py

# 运行完整测试序列
test-all:
	python src/power_ctrl_cli.py test

# 运行关机系统测试
test-shutdown:
	python tests/test_shutdown_system.py

# LED控制命令
led-normal:
	python src/power_ctrl_cli.py led normal

led-breath:
	python src/power_ctrl_cli.py led breath

# 呼吸周期控制命令
breath-1s:
	python src/power_ctrl_cli.py breath 1

breath-3s:
	python src/power_ctrl_cli.py breath 3

breath-5s:
	python src/power_ctrl_cli.py breath 5

# 系统命令
shutdown:
	python src/power_ctrl_cli.py shutdown

status:
	python src/power_ctrl_cli.py status

# 关机系统管理
install-shutdown:
	sudo scripts/install_shutdown_system.sh

service-status:
	systemctl status n100-shutdown.service

service-start:
	sudo systemctl start n100-shutdown.service

service-stop:
	sudo systemctl stop n100-shutdown.service

service-restart:
	sudo systemctl restart n100-shutdown.service

# 查看日志
logs:
	tail -f /var/log/n100_shutdown.log

service-logs:
	journalctl -u n100-shutdown.service -f

# 串口管理命令
check-serial:
	python src/serial_manager.py --check

release-serial:
	python src/serial_manager.py --force-release

test-serial:
	python src/serial_manager.py --test

# 使用新的串口管理命令
check:
	python src/power_ctrl_cli.py check

force-release:
	python src/power_ctrl_cli.py force-release

# 清理临时文件
clean:
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +
	find . -name "*.log" -delete
	python src/serial_manager.py --force-release
