# N100关机系统说明文档

## 概述

N100关机系统实现了完整的电源板与N100之间的关机协议，包括：

1. **监听关机请求**: 监听电源板发送的关机请求消息
2. **自动应答**: 收到关机请求后立即发送ACK应答
3. **执行关机**: 启动系统关机流程
4. **关机通知**: 在文件系统关闭前发送关机成功消息给电源板

## 系统架构

```
电源板 ←→ ttyS4 ←→ N100
    ↓              ↓
关机请求(0x13)   关机守护进程
    ↓              ↓
ACK应答(0x80)    系统关机
    ↓              ↓
关机成功(0x03)   关机钩子
```

## 消息帧格式

### 关机请求帧（电源板 → N100）
```
AA 01 13 EC 55
```
- 帧头: AA
- 长度: 01 (只有命令)
- 命令: 13 (关机请求)
- 校验: EC (校验和)
- 帧尾: 55

### ACK应答帧（N100 → 电源板）
```
AA 01 80 80 55
```
- 帧头: AA
- 长度: 01
- 命令: 80 (通用应答)
- 校验: 80
- 帧尾: 55

### 关机成功帧（N100 → 电源板）
```
AA 01 03 FD 55
```
- 帧头: AA
- 长度: 01
- 命令: 03 (关机成功)
- 校验: FD
- 帧尾: 55

## 文件结构

```
n100host/ctrl/
├── n100_power_ctrl.py          # 核心控制器类（扩展了关机请求监听）
├── n100_shutdown_daemon.py     # 关机守护进程
├── shutdown_notify.py          # 关机通知脚本
├── n100-shutdown.service       # systemd服务文件
├── n100-shutdown-hook.sh       # 关机钩子脚本
├── install_shutdown_system.sh  # 安装脚本
├── test_shutdown_system.py     # 测试脚本
└── SHUTDOWN_SYSTEM.md          # 本文档
```

## 安装步骤

### 1. 自动安装（推荐）

```bash
# 切换到root用户
sudo su

# 进入程序目录
cd /path/to/n100host/ctrl

# 运行安装脚本
chmod +x install_shutdown_system.sh
./install_shutdown_system.sh
```

### 2. 手动安装

```bash
# 1. 安装依赖
pip3 install pyserial

# 2. 创建目录
mkdir -p /opt/n100/ctrl
mkdir -p /var/log

# 3. 复制文件
cp n100_power_ctrl.py /opt/n100/ctrl/
cp n100_shutdown_daemon.py /opt/n100/ctrl/
cp shutdown_notify.py /opt/n100/ctrl/
cp n100-shutdown-hook.sh /opt/n100/ctrl/
chmod +x /opt/n100/ctrl/*.py
chmod +x /opt/n100/ctrl/*.sh

# 4. 安装systemd服务
cp n100-shutdown.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable n100-shutdown.service

# 5. 设置关机钩子
# 创建关机通知服务
cat > /etc/systemd/system/n100-shutdown-notify.service << 'EOF'
[Unit]
Description=N100 Shutdown Notification
DefaultDependencies=false
Before=shutdown.target reboot.target halt.target
Requires=shutdown.target

[Service]
Type=oneshot
RemainAfterExit=true
ExecStart=/bin/true
ExecStop=/opt/n100/ctrl/n100-shutdown-hook.sh
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable n100-shutdown-notify.service

# 6. 配置串口权限
chmod 666 /dev/ttyS4
echo 'SUBSYSTEM=="tty", KERNEL=="ttyS4", MODE="0666", GROUP="dialout"' > /etc/udev/rules.d/99-n100-serial.rules
udevadm control --reload-rules

# 7. 启动服务
systemctl start n100-shutdown.service
systemctl start n100-shutdown-notify.service
```

## 使用方法

### 启动关机守护进程

```bash
# 使用systemd（推荐）
systemctl start n100-shutdown.service
systemctl status n100-shutdown.service

# 手动启动（调试用）
python3 /opt/n100/ctrl/n100_shutdown_daemon.py --port /dev/ttyS4
```

### 测试系统

```bash
# 运行测试脚本
python3 /opt/n100/ctrl/test_shutdown_system.py

# 手动测试关机通知
python3 /opt/n100/ctrl/shutdown_notify.py

# 模拟发送关机请求（需要另一个设备或程序）
echo -ne '\xAA\x01\x13\xEC\x55' > /dev/ttyS4
```

### 查看日志

```bash
# 查看关机日志
tail -f /var/log/n100_shutdown.log

# 查看systemd日志
journalctl -u n100-shutdown.service -f
journalctl -u n100-shutdown-notify.service -f
```

## 工作流程

### 正常关机流程

1. **监听阶段**: 关机守护进程持续监听ttyS4端口
2. **接收请求**: 电源板发送关机请求帧 `AA 01 13 EC 55`
3. **立即应答**: N100发送ACK应答帧 `AA 01 80 80 55`
4. **执行关机**: 调用 `sudo shutdown -h now`
5. **关机钩子**: systemd在关机时执行关机通知服务
6. **发送成功**: 发送关机成功帧 `AA 01 03 FD 55`
7. **系统关闭**: 完成关机流程

### 异常处理

- **串口连接失败**: 守护进程会记录错误并尝试重连
- **关机请求解析失败**: 忽略无效帧，继续监听
- **ACK发送失败**: 记录错误但继续执行关机
- **关机成功消息发送失败**: 使用备用方法直接发送

## 配置选项

### 关机守护进程参数

```bash
python3 n100_shutdown_daemon.py [选项]

选项:
  --port PORT           串口设备路径 (默认: /dev/ttyS4)
  --log-file FILE       日志文件路径 (默认: /var/log/n100_shutdown.log)
  --daemon              以守护进程模式运行
  --send-shutdown-success  仅发送关机成功消息
```

### 环境变量

- `PYTHONPATH`: Python模块搜索路径
- `N100_SERIAL_PORT`: 串口设备路径（默认: /dev/ttyS4）

## 故障排除

### 常见问题

1. **串口权限不足**
   ```bash
   sudo chmod 666 /dev/ttyS4
   sudo usermod -a -G dialout $USER
   ```

2. **服务启动失败**
   ```bash
   systemctl status n100-shutdown.service
   journalctl -u n100-shutdown.service
   ```

3. **关机钩子不执行**
   ```bash
   systemctl status n100-shutdown-notify.service
   ls -la /opt/n100/ctrl/n100-shutdown-hook.sh
   ```

4. **Python模块导入失败**
   ```bash
   export PYTHONPATH=/opt/n100/ctrl:$PYTHONPATH
   python3 -c "import n100_power_ctrl; print('OK')"
   ```

### 调试模式

```bash
# 前台运行守护进程（调试用）
python3 /opt/n100/ctrl/n100_shutdown_daemon.py --port /dev/ttyS4

# 启用详细日志
export PYTHONPATH=/opt/n100/ctrl
python3 -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from n100_shutdown_daemon import N100ShutdownDaemon
daemon = N100ShutdownDaemon()
daemon.start()
"
```

## 安全注意事项

1. **权限控制**: 关机守护进程需要root权限执行关机命令
2. **串口安全**: 确保只有授权设备能访问ttyS4
3. **日志监控**: 定期检查关机日志，发现异常行为
4. **备份机制**: 建议设置硬件看门狗作为备用关机机制

## 性能指标

- **响应时间**: 收到关机请求后1秒内发送ACK应答
- **关机时间**: 从收到请求到系统关闭通常在30秒内
- **可靠性**: 支持自动重试和异常恢复
- **资源占用**: 守护进程CPU占用 < 1%，内存占用 < 10MB

## 维护建议

1. **定期检查**: 每周检查服务状态和日志
2. **测试验证**: 每月进行一次完整的关机测试
3. **日志轮转**: 配置logrotate防止日志文件过大
4. **版本更新**: 关注程序更新，及时升级

## 技术支持

如遇到问题，请提供以下信息：

1. 系统版本和硬件信息
2. 错误日志内容
3. 服务状态信息
4. 串口设备状态

联系方式：请参考项目文档或提交issue。
